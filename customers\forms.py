from django import forms
from .models import Customer, CustomerTransaction


class CustomerForm(forms.ModelForm):
    """نموذج العميل"""
    
    class Meta:
        model = Customer
        fields = ['name', 'phone', 'email', 'address', 'credit_limit']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'phone': forms.TextInput(attrs={
                'class': 'form-control', 
                'dir': 'ltr',
                'placeholder': '+20xxxxxxxxx'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control', 
                'dir': 'ltr'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control', 
                'rows': 3
            }),
            'credit_limit': forms.NumberInput(attrs={
                'class': 'form-control', 
                'step': '0.01',
                'min': '0'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # إضافة تلميحات
        self.fields['phone'].help_text = 'رقم الهاتف مع كود الدولة'
        self.fields['credit_limit'].help_text = 'الحد الأقصى للائتمان (0 = نقدي فقط)'
        
        # جعل بعض الحقول مطلوبة
        self.fields['name'].required = True
    
    def clean_phone(self):
        """التحقق من صحة رقم الهاتف"""
        phone = self.cleaned_data.get('phone')
        
        if phone:
            # إزالة المسافات والرموز
            phone = phone.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
            
            # التحقق من أن الرقم يحتوي على أرقام فقط
            if not phone.isdigit():
                raise forms.ValidationError('رقم الهاتف يجب أن يحتوي على أرقام فقط')
            
            # التحقق من طول الرقم
            if len(phone) < 10 or len(phone) > 15:
                raise forms.ValidationError('رقم الهاتف غير صحيح')
        
        return phone
    
    def clean_email(self):
        """التحقق من فرادة البريد الإلكتروني"""
        email = self.cleaned_data.get('email')
        
        if email:
            # التحقق من عدم وجود عميل آخر بنفس البريد
            existing = Customer.objects.filter(email=email, is_active=True)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise forms.ValidationError('يوجد عميل آخر بنفس البريد الإلكتروني')
        
        return email


class CustomerTransactionForm(forms.ModelForm):
    """نموذج معاملة العميل"""
    
    class Meta:
        model = CustomerTransaction
        fields = ['transaction_type', 'amount', 'reference', 'description']
        widgets = {
            'transaction_type': forms.Select(attrs={'class': 'form-select'}),
            'amount': forms.NumberInput(attrs={
                'class': 'form-control', 
                'step': '0.01',
                'min': '0.01'
            }),
            'reference': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={
                'class': 'form-control', 
                'rows': 3
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # إضافة تلميحات
        self.fields['reference'].help_text = 'رقم الفاتورة أو المرجع'
        self.fields['amount'].help_text = 'المبلغ بالجنيه المصري'
        
        # جعل بعض الحقول مطلوبة
        self.fields['transaction_type'].required = True
        self.fields['amount'].required = True
        self.fields['description'].required = True
    
    def clean_amount(self):
        """التحقق من صحة المبلغ"""
        amount = self.cleaned_data.get('amount')
        
        if amount and amount <= 0:
            raise forms.ValidationError('المبلغ يجب أن يكون أكبر من صفر')
        
        return amount


class CustomerSearchForm(forms.Form):
    """نموذج البحث عن العملاء"""
    
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالاسم أو الهاتف أو البريد الإلكتروني...'
        })
    )
    
    has_balance = forms.ChoiceField(
        choices=[
            ('', 'الكل'),
            ('yes', 'لديه رصيد'),
            ('no', 'ليس لديه رصيد'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    order_by = forms.ChoiceField(
        choices=[
            ('name', 'الاسم'),
            ('current_balance', 'الرصيد'),
            ('created_at', 'تاريخ الإنشاء'),
        ],
        initial='name',
        widget=forms.Select(attrs={'class': 'form-select'})
    )


class CustomerBalanceAdjustmentForm(forms.Form):
    """نموذج تسوية رصيد العميل"""
    
    adjustment_type = forms.ChoiceField(
        choices=[
            ('increase', 'زيادة الرصيد'),
            ('decrease', 'تقليل الرصيد'),
            ('set', 'تحديد الرصيد'),
        ],
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    amount = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=0.01,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.01'
        })
    )
    
    reason = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'سبب التسوية...'
        })
    )
    
    def __init__(self, customer=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.customer = customer
        
        if customer:
            self.fields['amount'].help_text = f'الرصيد الحالي: {customer.current_balance} ج.م'


class CustomerStatementForm(forms.Form):
    """نموذج كشف حساب العميل"""
    
    date_from = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_to = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    transaction_type = forms.ChoiceField(
        choices=[('', 'جميع المعاملات')] + CustomerTransaction.TRANSACTION_TYPES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    format = forms.ChoiceField(
        choices=[
            ('html', 'عرض على الشاشة'),
            ('pdf', 'تصدير PDF'),
            ('excel', 'تصدير Excel'),
        ],
        initial='html',
        widget=forms.Select(attrs={'class': 'form-select'})
    )
