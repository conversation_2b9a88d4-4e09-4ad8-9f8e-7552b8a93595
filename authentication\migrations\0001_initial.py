# Generated by Django 4.2.7 on 2025-07-10 13:06

from django.conf import settings
import django.contrib.auth.models
import django.contrib.auth.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('role', models.CharField(choices=[('admin', 'مدير'), ('cashier', 'كاشير'), ('accountant', 'محاسب'), ('warehouse_manager', 'مشرف مخازن')], default='cashier', max_length=20, verbose_name='الدور')),
                ('phone', models.CharField(blank=True, max_length=15, null=True, verbose_name='رقم الهاتف')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('is_active_employee', models.BooleanField(default=True, verbose_name='موظف نشط')),
                ('hire_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ التوظيف')),
                ('salary', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الراتب')),
                ('profile_image', models.ImageField(blank=True, null=True, upload_to='profiles/', verbose_name='صورة الملف الشخصي')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'مستخدم',
                'verbose_name_plural': 'المستخدمون',
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='UserSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_key', models.CharField(max_length=40, unique=True, verbose_name='مفتاح الجلسة')),
                ('ip_address', models.GenericIPAddressField(verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='معلومات المتصفح')),
                ('login_time', models.DateTimeField(auto_now_add=True, verbose_name='وقت تسجيل الدخول')),
                ('last_activity', models.DateTimeField(auto_now=True, verbose_name='آخر نشاط')),
                ('is_active', models.BooleanField(default=True, verbose_name='جلسة نشطة')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sessions', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'جلسة مستخدم',
                'verbose_name_plural': 'جلسات المستخدمين',
                'ordering': ['-login_time'],
            },
        ),
        migrations.CreateModel(
            name='UserActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('login', 'تسجيل دخول'), ('logout', 'تسجيل خروج'), ('create', 'إنشاء'), ('update', 'تحديث'), ('delete', 'حذف'), ('view', 'عرض'), ('sale', 'بيع'), ('return', 'مرتجع'), ('payment', 'دفع'), ('report', 'تقرير')], max_length=20, verbose_name='النشاط')),
                ('description', models.TextField(verbose_name='الوصف')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='وقت النشاط')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'نشاط مستخدم',
                'verbose_name_plural': 'نشاطات المستخدمين',
                'ordering': ['-timestamp'],
            },
        ),
    ]
