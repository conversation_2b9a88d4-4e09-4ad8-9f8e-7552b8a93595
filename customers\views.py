from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView
from django.urls import reverse_lazy
from django.contrib import messages
from django.db.models import Q, Sum
from django.http import JsonResponse

from .models import Customer, CustomerTransaction
from .forms import CustomerForm, CustomerTransactionForm


class CustomerListView(LoginRequiredMixin, ListView):
    """قائمة العملاء"""
    model = Customer
    template_name = 'customers/customer_list.html'
    context_object_name = 'customers'
    paginate_by = 20

    def get_queryset(self):
        queryset = Customer.objects.filter(is_active=True).order_by('name')

        # البحث
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(phone__icontains=search) |
                Q(email__icontains=search)
            )

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['total_customers'] = self.get_queryset().count()
        context['total_balance'] = self.get_queryset().aggregate(
            total=Sum('current_balance')
        )['total'] or 0
        return context


class CustomerCreateView(LoginRequiredMixin, CreateView):
    """إنشاء عميل جديد"""
    model = Customer
    form_class = CustomerForm
    template_name = 'customers/customer_form.html'
    success_url = reverse_lazy('customers:customer_list')

    def form_valid(self, form):
        messages.success(self.request, f'تم إنشاء العميل {form.instance.name} بنجاح')
        return super().form_valid(form)


class CustomerDetailView(LoginRequiredMixin, DetailView):
    """تفاصيل العميل"""
    model = Customer
    template_name = 'customers/customer_detail.html'
    context_object_name = 'customer'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['transactions'] = CustomerTransaction.objects.filter(
            customer=self.object
        ).select_related('created_by').order_by('-created_at')[:10]

        # إحصائيات العميل
        context['total_sales'] = CustomerTransaction.objects.filter(
            customer=self.object,
            transaction_type='sale'
        ).aggregate(total=Sum('amount'))['total'] or 0

        context['total_payments'] = CustomerTransaction.objects.filter(
            customer=self.object,
            transaction_type='payment'
        ).aggregate(total=Sum('amount'))['total'] or 0

        return context


class CustomerUpdateView(LoginRequiredMixin, UpdateView):
    """تحديث بيانات العميل"""
    model = Customer
    form_class = CustomerForm
    template_name = 'customers/customer_form.html'
    success_url = reverse_lazy('customers:customer_list')

    def form_valid(self, form):
        messages.success(self.request, f'تم تحديث بيانات العميل {form.instance.name} بنجاح')
        return super().form_valid(form)


class CustomerDeleteView(LoginRequiredMixin, DeleteView):
    """حذف عميل"""
    model = Customer
    template_name = 'customers/customer_confirm_delete.html'
    success_url = reverse_lazy('customers:customer_list')

    def delete(self, request, *args, **kwargs):
        customer = self.get_object()
        customer.is_active = False
        customer.save()
        messages.success(request, f'تم إلغاء تفعيل العميل {customer.name} بنجاح')
        return redirect(self.success_url)


class CustomerTransactionListView(LoginRequiredMixin, ListView):
    """قائمة معاملات العميل"""
    model = CustomerTransaction
    template_name = 'customers/customer_transactions.html'
    context_object_name = 'transactions'
    paginate_by = 50

    def get_queryset(self):
        customer_id = self.kwargs.get('pk')
        return CustomerTransaction.objects.filter(
            customer_id=customer_id
        ).select_related('created_by').order_by('-created_at')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        customer_id = self.kwargs.get('pk')
        context['customer'] = get_object_or_404(Customer, pk=customer_id)
        return context


class CustomerTransactionCreateView(LoginRequiredMixin, CreateView):
    """إنشاء معاملة عميل جديدة"""
    model = CustomerTransaction
    form_class = CustomerTransactionForm
    template_name = 'customers/customer_transaction_form.html'

    def get_success_url(self):
        return reverse_lazy('customers:customer_transactions',
                          kwargs={'pk': self.object.customer.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        customer_id = self.kwargs.get('pk')
        context['customer'] = get_object_or_404(Customer, pk=customer_id)
        return context

    def form_valid(self, form):
        customer_id = self.kwargs.get('pk')
        form.instance.customer = get_object_or_404(Customer, pk=customer_id)
        form.instance.created_by = self.request.user

        messages.success(self.request, 'تم إنشاء المعاملة بنجاح')
        return super().form_valid(form)
