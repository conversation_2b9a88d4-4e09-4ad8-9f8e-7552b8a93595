from django.shortcuts import render, get_object_or_404
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import TemplateView, ListView
from django.http import HttpResponse
from django.contrib.auth.decorators import login_required

from .models import ReportTemplate, GeneratedReport, ReportSchedule


class ReportDashboardView(LoginRequiredMixin, TemplateView):
    """لوحة تحكم التقارير"""
    template_name = 'reports/dashboard.html'


class SalesReportView(LoginRequiredMixin, TemplateView):
    """تقارير المبيعات"""
    template_name = 'reports/sales_report.html'


class InventoryReportView(LoginRequiredMixin, TemplateView):
    """تقارير المخزون"""
    template_name = 'reports/inventory_report.html'


class FinancialReportView(LoginRequiredMixin, TemplateView):
    """التقارير المالية"""
    template_name = 'reports/financial_report.html'


class CustomerReportView(LoginRequiredMixin, TemplateView):
    """تقارير العملاء"""
    template_name = 'reports/customer_report.html'


class ReportTemplateListView(LoginRequiredMixin, ListView):
    """قائمة قوالب التقارير"""
    model = ReportTemplate
    template_name = 'reports/template_list.html'
    context_object_name = 'templates'


class GeneratedReportListView(LoginRequiredMixin, ListView):
    """قائمة التقارير المُولدة"""
    model = GeneratedReport
    template_name = 'reports/generated_list.html'
    context_object_name = 'reports'


@login_required
def generate_report(request, template_id):
    """توليد تقرير"""
    template = get_object_or_404(ReportTemplate, pk=template_id)

    # هنا يمكن إضافة منطق توليد التقرير
    # مؤقتاً سنعرض رسالة

    return HttpResponse(f"تم توليد التقرير: {template.name}")
