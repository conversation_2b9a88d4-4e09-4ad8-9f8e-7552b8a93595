from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone


class Department(models.Model):
    """الأقسام"""

    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name='اسم القسم'
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name='الوصف'
    )
    manager = models.ForeignKey(
        'authentication.User',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='managed_departments',
        verbose_name='مدير القسم'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    class Meta:
        verbose_name = 'قسم'
        verbose_name_plural = 'الأقسام'
        ordering = ['name']

    def __str__(self):
        return self.name


class Shift(models.Model):
    """الورديات"""

    name = models.CharField(
        max_length=100,
        verbose_name='اسم الوردية'
    )
    start_time = models.TimeField(
        verbose_name='وقت البداية'
    )
    end_time = models.TimeField(
        verbose_name='وقت النهاية'
    )
    break_duration = models.IntegerField(
        default=0,
        help_text='مدة الاستراحة بالدقائق',
        verbose_name='مدة الاستراحة'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشطة'
    )

    class Meta:
        verbose_name = 'وردية'
        verbose_name_plural = 'الورديات'
        ordering = ['start_time']

    def __str__(self):
        return f"{self.name} ({self.start_time} - {self.end_time})"


class EmployeeProfile(models.Model):
    """ملف الموظف"""

    user = models.OneToOneField(
        'authentication.User',
        on_delete=models.CASCADE,
        related_name='employee_profile',
        verbose_name='المستخدم'
    )
    employee_id = models.CharField(
        max_length=20,
        unique=True,
        verbose_name='رقم الموظف'
    )
    department = models.ForeignKey(
        Department,
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='employees',
        verbose_name='القسم'
    )
    position = models.CharField(
        max_length=100,
        verbose_name='المنصب'
    )
    shift = models.ForeignKey(
        Shift,
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='employees',
        verbose_name='الوردية'
    )
    national_id = models.CharField(
        max_length=20,
        unique=True,
        verbose_name='رقم الهوية'
    )
    emergency_contact = models.CharField(
        max_length=15,
        blank=True,
        null=True,
        verbose_name='جهة اتصال الطوارئ'
    )
    bank_account = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='رقم الحساب البنكي'
    )

    class Meta:
        verbose_name = 'ملف موظف'
        verbose_name_plural = 'ملفات الموظفين'
        ordering = ['employee_id']

    def __str__(self):
        return f"{self.employee_id} - {self.user.get_full_name()}"


class Attendance(models.Model):
    """الحضور والانصراف"""

    employee = models.ForeignKey(
        'authentication.User',
        on_delete=models.CASCADE,
        related_name='attendance_records',
        verbose_name='الموظف'
    )
    date = models.DateField(
        default=timezone.now,
        verbose_name='التاريخ'
    )
    check_in = models.TimeField(
        blank=True,
        null=True,
        verbose_name='وقت الحضور'
    )
    check_out = models.TimeField(
        blank=True,
        null=True,
        verbose_name='وقت الانصراف'
    )
    break_start = models.TimeField(
        blank=True,
        null=True,
        verbose_name='بداية الاستراحة'
    )
    break_end = models.TimeField(
        blank=True,
        null=True,
        verbose_name='نهاية الاستراحة'
    )
    total_hours = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        verbose_name='إجمالي الساعات'
    )
    overtime_hours = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        verbose_name='ساعات إضافية'
    )
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )

    class Meta:
        verbose_name = 'حضور'
        verbose_name_plural = 'سجل الحضور'
        unique_together = ['employee', 'date']
        ordering = ['-date']

    def __str__(self):
        return f"{self.employee.get_full_name()} - {self.date}"


class Payroll(models.Model):
    """كشف الراتب"""

    employee = models.ForeignKey(
        'authentication.User',
        on_delete=models.CASCADE,
        related_name='payrolls',
        verbose_name='الموظف'
    )
    month = models.IntegerField(
        verbose_name='الشهر'
    )
    year = models.IntegerField(
        verbose_name='السنة'
    )
    basic_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='الراتب الأساسي'
    )
    allowances = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name='البدلات'
    )
    overtime_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name='مبلغ الساعات الإضافية'
    )
    deductions = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name='الخصومات'
    )
    net_salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='صافي الراتب'
    )
    is_paid = models.BooleanField(
        default=False,
        verbose_name='مدفوع'
    )
    payment_date = models.DateField(
        blank=True,
        null=True,
        verbose_name='تاريخ الدفع'
    )
    created_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        related_name='created_payrolls',
        verbose_name='أنشأ بواسطة'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    class Meta:
        verbose_name = 'كشف راتب'
        verbose_name_plural = 'كشوف الرواتب'
        unique_together = ['employee', 'month', 'year']
        ordering = ['-year', '-month']

    def __str__(self):
        return f"{self.employee.get_full_name()} - {self.month}/{self.year}"

    def calculate_net_salary(self):
        """حساب صافي الراتب"""
        self.net_salary = (
            self.basic_salary +
            self.allowances +
            self.overtime_amount -
            self.deductions
        )
