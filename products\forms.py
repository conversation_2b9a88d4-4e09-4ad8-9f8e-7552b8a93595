from django import forms
from .models import Product, Category, StockMovement


class ProductForm(forms.ModelForm):
    """نموذج المنتج"""
    
    class Meta:
        model = Product
        fields = ['name', 'code', 'barcode', 'category', 'description', 'image',
                 'cost_price', 'selling_price', 'quantity', 'min_quantity', 'unit']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'code': forms.TextInput(attrs={'class': 'form-control', 'dir': 'ltr'}),
            'barcode': forms.TextInput(attrs={'class': 'form-control', 'dir': 'ltr'}),
            'category': forms.Select(attrs={'class': 'form-select'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'image': forms.FileInput(attrs={'class': 'form-control'}),
            'cost_price': forms.NumberInput(attrs={
                'class': 'form-control', 
                'step': '0.01',
                'min': '0'
            }),
            'selling_price': forms.NumberInput(attrs={
                'class': 'form-control', 
                'step': '0.01',
                'min': '0'
            }),
            'quantity': forms.NumberInput(attrs={
                'class': 'form-control', 
                'min': '0'
            }),
            'min_quantity': forms.NumberInput(attrs={
                'class': 'form-control', 
                'min': '0'
            }),
            'unit': forms.TextInput(attrs={'class': 'form-control'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['category'].queryset = Category.objects.filter(is_active=True)
        
        # إضافة تلميحات
        self.fields['code'].help_text = 'كود فريد للمنتج'
        self.fields['barcode'].help_text = 'اتركه فارغاً لتوليد تلقائي'
        self.fields['min_quantity'].help_text = 'الحد الأدنى للتنبيه'
    
    def clean_code(self):
        """التحقق من فرادة الكود"""
        code = self.cleaned_data['code']
        
        # التحقق من عدم وجود منتج آخر بنفس الكود
        existing = Product.objects.filter(code=code)
        if self.instance.pk:
            existing = existing.exclude(pk=self.instance.pk)
        
        if existing.exists():
            raise forms.ValidationError('يوجد منتج آخر بنفس الكود')
        
        return code
    
    def clean_barcode(self):
        """التحقق من فرادة الباركود"""
        barcode = self.cleaned_data['barcode']
        
        if barcode:
            # التحقق من عدم وجود منتج آخر بنفس الباركود
            existing = Product.objects.filter(barcode=barcode)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise forms.ValidationError('يوجد منتج آخر بنفس الباركود')
        
        return barcode
    
    def clean(self):
        """التحقق من صحة البيانات"""
        cleaned_data = super().clean()
        cost_price = cleaned_data.get('cost_price')
        selling_price = cleaned_data.get('selling_price')
        
        if cost_price and selling_price:
            if selling_price < cost_price:
                raise forms.ValidationError('سعر البيع يجب أن يكون أكبر من سعر التكلفة')
        
        return cleaned_data


class CategoryForm(forms.ModelForm):
    """نموذج التصنيف"""
    
    class Meta:
        model = Category
        fields = ['name', 'description', 'parent']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'parent': forms.Select(attrs={'class': 'form-select'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['parent'].queryset = Category.objects.filter(is_active=True)
        self.fields['parent'].empty_label = "تصنيف رئيسي"
        
        # منع اختيار التصنيف نفسه كأب
        if self.instance.pk:
            self.fields['parent'].queryset = self.fields['parent'].queryset.exclude(
                pk=self.instance.pk
            )
    
    def clean_name(self):
        """التحقق من فرادة الاسم"""
        name = self.cleaned_data['name']
        
        # التحقق من عدم وجود تصنيف آخر بنفس الاسم
        existing = Category.objects.filter(name=name, is_active=True)
        if self.instance.pk:
            existing = existing.exclude(pk=self.instance.pk)
        
        if existing.exists():
            raise forms.ValidationError('يوجد تصنيف آخر بنفس الاسم')
        
        return name


class StockMovementForm(forms.ModelForm):
    """نموذج حركة المخزون"""
    
    class Meta:
        model = StockMovement
        fields = ['product', 'movement_type', 'quantity', 'reference', 'notes']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select'}),
            'movement_type': forms.Select(attrs={'class': 'form-select'}),
            'quantity': forms.NumberInput(attrs={
                'class': 'form-control', 
                'min': '1'
            }),
            'reference': forms.TextInput(attrs={'class': 'form-control'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['product'].queryset = Product.objects.filter(is_active=True)
        
        # إضافة تلميحات
        self.fields['reference'].help_text = 'رقم الفاتورة أو المرجع'
        self.fields['notes'].help_text = 'ملاحظات إضافية'
    
    def clean(self):
        """التحقق من صحة البيانات"""
        cleaned_data = super().clean()
        product = cleaned_data.get('product')
        movement_type = cleaned_data.get('movement_type')
        quantity = cleaned_data.get('quantity')
        
        if product and movement_type and quantity:
            # التحقق من توفر الكمية في حالة الإخراج
            if movement_type in ['out', 'adjustment'] and quantity > product.quantity:
                raise forms.ValidationError(
                    f'الكمية المطلوبة ({quantity}) أكبر من المتوفر ({product.quantity})'
                )
        
        return cleaned_data


class ProductSearchForm(forms.Form):
    """نموذج البحث عن المنتجات"""
    
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالاسم أو الكود أو الباركود...'
        })
    )
    
    category = forms.ModelChoiceField(
        queryset=Category.objects.filter(is_active=True),
        required=False,
        empty_label="جميع التصنيفات",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    order_by = forms.ChoiceField(
        choices=[
            ('name', 'الاسم'),
            ('code', 'الكود'),
            ('selling_price', 'السعر'),
            ('quantity', 'الكمية'),
            ('created_at', 'تاريخ الإنشاء'),
        ],
        initial='name',
        widget=forms.Select(attrs={'class': 'form-select'})
    )


class BulkUpdateForm(forms.Form):
    """نموذج التحديث المجمع للمنتجات"""
    
    action = forms.ChoiceField(
        choices=[
            ('update_prices', 'تحديث الأسعار'),
            ('update_quantities', 'تحديث الكميات'),
            ('change_category', 'تغيير التصنيف'),
            ('deactivate', 'إلغاء التفعيل'),
        ],
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    price_increase_percentage = forms.DecimalField(
        max_digits=5,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.01'
        })
    )
    
    new_category = forms.ModelChoiceField(
        queryset=Category.objects.filter(is_active=True),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    selected_products = forms.CharField(
        widget=forms.HiddenInput()
    )
