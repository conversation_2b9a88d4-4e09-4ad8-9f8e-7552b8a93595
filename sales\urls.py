from django.urls import path
from . import views

app_name = 'sales'

urlpatterns = [
    path('', views.SaleListView.as_view(), name='sale_list'),
    path('pos/', views.POSView.as_view(), name='pos'),
    path('create/', views.SaleCreateView.as_view(), name='sale_create'),
    path('<int:pk>/', views.SaleDetailView.as_view(), name='sale_detail'),
    path('<int:pk>/edit/', views.SaleUpdateView.as_view(), name='sale_edit'),
    path('<int:pk>/delete/', views.SaleDeleteView.as_view(), name='sale_delete'),
    path('<int:pk>/print/', views.print_invoice, name='print_invoice'),
    path('returns/', views.SaleReturnListView.as_view(), name='return_list'),
    path('returns/create/', views.SaleReturnCreateView.as_view(), name='return_create'),
    path('api/products/search/', views.product_search_api, name='product_search_api'),
]
