from django.shortcuts import render, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import TemplateView, ListView, CreateView, UpdateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse

from .models import SystemSettings, Branch, Notification, BackupLog


class SettingsView(LoginRequiredMixin, TemplateView):
    """صفحة الإعدادات الرئيسية"""
    template_name = 'settings_app/settings.html'


class SystemSettingsView(LoginRequiredMixin, UpdateView):
    """إعدادات النظام"""
    model = SystemSettings
    template_name = 'settings_app/system_settings.html'
    fields = ['company_name', 'company_address', 'company_phone', 'company_email',
             'tax_enabled', 'tax_rate', 'currency_code', 'currency_symbol',
             'language', 'theme']
    success_url = reverse_lazy('settings_app:system_settings')

    def get_object(self):
        return SystemSettings.get_settings()

    def form_valid(self, form):
        messages.success(self.request, 'تم حفظ الإعدادات بنجاح')
        return super().form_valid(form)


class BranchListView(LoginRequiredMixin, ListView):
    """قائمة الفروع"""
    model = Branch
    template_name = 'settings_app/branch_list.html'
    context_object_name = 'branches'


class BranchCreateView(LoginRequiredMixin, CreateView):
    """إنشاء فرع جديد"""
    model = Branch
    template_name = 'settings_app/branch_form.html'
    fields = ['name', 'address', 'phone', 'manager']
    success_url = reverse_lazy('settings_app:branch_list')

    def form_valid(self, form):
        messages.success(self.request, f'تم إنشاء الفرع {form.instance.name} بنجاح')
        return super().form_valid(form)


class BackupView(LoginRequiredMixin, TemplateView):
    """صفحة النسخ الاحتياطي"""
    template_name = 'settings_app/backup.html'


class BackupLogListView(LoginRequiredMixin, ListView):
    """سجل النسخ الاحتياطي"""
    model = BackupLog
    template_name = 'settings_app/backup_logs.html'
    context_object_name = 'logs'


@login_required
def create_backup(request):
    """إنشاء نسخة احتياطية"""

    # هنا يمكن إضافة منطق إنشاء النسخة الاحتياطية
    # مؤقتاً سنعرض رسالة

    messages.success(request, 'تم إنشاء النسخة الاحتياطية بنجاح')
    return redirect('settings_app:backup')
