from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone


class Customer(models.Model):
    """العملاء"""

    name = models.CharField(
        max_length=200,
        verbose_name='اسم العميل'
    )
    phone = models.CharField(
        max_length=15,
        blank=True,
        null=True,
        verbose_name='رقم الهاتف'
    )
    email = models.EmailField(
        blank=True,
        null=True,
        verbose_name='البريد الإلكتروني'
    )
    address = models.TextField(
        blank=True,
        null=True,
        verbose_name='العنوان'
    )
    credit_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='الحد الائتماني'
    )
    current_balance = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name='الرصيد الحالي'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    class Meta:
        verbose_name = 'عميل'
        verbose_name_plural = 'العملاء'
        ordering = ['name']

    def __str__(self):
        return self.name

    @property
    def available_credit(self):
        """الائتمان المتاح"""
        return self.credit_limit - self.current_balance

    @property
    def is_credit_exceeded(self):
        """تحقق من تجاوز الحد الائتماني"""
        return self.current_balance > self.credit_limit


class CustomerTransaction(models.Model):
    """معاملات العملاء"""

    TRANSACTION_TYPES = [
        ('sale', 'بيع'),
        ('payment', 'دفع'),
        ('return', 'مرتجع'),
        ('adjustment', 'تسوية'),
    ]

    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='transactions',
        verbose_name='العميل'
    )
    transaction_type = models.CharField(
        max_length=20,
        choices=TRANSACTION_TYPES,
        verbose_name='نوع المعاملة'
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='المبلغ'
    )
    reference = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='المرجع'
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name='الوصف'
    )
    created_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        verbose_name='أنشأ بواسطة'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ المعاملة'
    )

    class Meta:
        verbose_name = 'معاملة عميل'
        verbose_name_plural = 'معاملات العملاء'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.customer.name} - {self.get_transaction_type_display()} - {self.amount}"

    def save(self, *args, **kwargs):
        """تحديث رصيد العميل عند حفظ المعاملة"""
        is_new = self.pk is None

        if is_new:
            # تحديث رصيد العميل
            if self.transaction_type in ['sale']:
                self.customer.current_balance += self.amount
            elif self.transaction_type in ['payment', 'return']:
                self.customer.current_balance -= self.amount
            elif self.transaction_type == 'adjustment':
                # التسوية يمكن أن تكون موجبة أو سالبة
                self.customer.current_balance += self.amount

            self.customer.save()

        super().save(*args, **kwargs)
