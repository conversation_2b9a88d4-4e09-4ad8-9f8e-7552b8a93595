from django import forms
from django.forms import inlineformset_factory
from .models import Sale, SaleItem, SaleReturn, SaleReturnItem
from customers.models import Customer
from products.models import Product


class SaleForm(forms.ModelForm):
    """نموذج البيع"""
    
    class Meta:
        model = Sale
        fields = ['customer', 'payment_method', 'discount_percentage', 'tax_percentage', 
                 'paid_amount', 'notes']
        widgets = {
            'customer': forms.Select(attrs={'class': 'form-select'}),
            'payment_method': forms.Select(attrs={'class': 'form-select'}),
            'discount_percentage': forms.NumberInput(attrs={
                'class': 'form-control', 
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'tax_percentage': forms.NumberInput(attrs={
                'class': 'form-control', 
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'paid_amount': forms.NumberInput(attrs={
                'class': 'form-control', 
                'step': '0.01',
                'min': '0'
            }),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['customer'].queryset = Customer.objects.filter(is_active=True)
        self.fields['customer'].empty_label = "عميل نقدي"


class SaleItemForm(forms.ModelForm):
    """نموذج عنصر البيع"""
    
    class Meta:
        model = SaleItem
        fields = ['product', 'quantity', 'unit_price']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select product-select'}),
            'quantity': forms.NumberInput(attrs={
                'class': 'form-control quantity-input', 
                'min': '1'
            }),
            'unit_price': forms.NumberInput(attrs={
                'class': 'form-control price-input', 
                'step': '0.01',
                'min': '0'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['product'].queryset = Product.objects.filter(
            is_active=True, 
            quantity__gt=0
        )


# إنشاء FormSet لعناصر البيع
SaleItemFormSet = inlineformset_factory(
    Sale, 
    SaleItem,
    form=SaleItemForm,
    extra=1,
    min_num=1,
    validate_min=True,
    can_delete=True
)


class SaleReturnForm(forms.ModelForm):
    """نموذج مرتجع البيع"""
    
    class Meta:
        model = SaleReturn
        fields = ['original_sale', 'return_amount', 'reason']
        widgets = {
            'original_sale': forms.Select(attrs={'class': 'form-select'}),
            'return_amount': forms.NumberInput(attrs={
                'class': 'form-control', 
                'step': '0.01',
                'min': '0'
            }),
            'reason': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['original_sale'].queryset = Sale.objects.filter(
            status='completed'
        ).order_by('-created_at')


class SaleReturnItemForm(forms.ModelForm):
    """نموذج عنصر مرتجع البيع"""
    
    class Meta:
        model = SaleReturnItem
        fields = ['product', 'quantity', 'unit_price']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select'}),
            'quantity': forms.NumberInput(attrs={
                'class': 'form-control', 
                'min': '1'
            }),
            'unit_price': forms.NumberInput(attrs={
                'class': 'form-control', 
                'step': '0.01',
                'min': '0'
            }),
        }


# إنشاء FormSet لعناصر مرتجع البيع
SaleReturnItemFormSet = inlineformset_factory(
    SaleReturn, 
    SaleReturnItem,
    form=SaleReturnItemForm,
    extra=1,
    min_num=1,
    validate_min=True,
    can_delete=True
)


class POSForm(forms.Form):
    """نموذج نقطة البيع"""
    
    customer = forms.ModelChoiceField(
        queryset=Customer.objects.filter(is_active=True),
        required=False,
        empty_label="عميل نقدي",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    payment_method = forms.ChoiceField(
        choices=Sale.PAYMENT_METHODS,
        initial='cash',
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    discount_percentage = forms.DecimalField(
        max_digits=5,
        decimal_places=2,
        initial=0,
        min_value=0,
        max_value=100,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.01'
        })
    )
    
    tax_percentage = forms.DecimalField(
        max_digits=5,
        decimal_places=2,
        initial=0,
        min_value=0,
        max_value=100,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.01'
        })
    )
    
    paid_amount = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.01'
        })
    )
    
    notes = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 2,
            'placeholder': 'ملاحظات إضافية...'
        })
    )
