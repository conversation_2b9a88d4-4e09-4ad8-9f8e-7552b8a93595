from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone
from decimal import Decimal


class Sale(models.Model):
    """المبيعات"""

    PAYMENT_METHODS = [
        ('cash', 'نقدي'),
        ('credit', 'آجل'),
        ('card', 'بطاقة'),
        ('mixed', 'مختلط'),
    ]

    STATUS_CHOICES = [
        ('pending', 'معلق'),
        ('completed', 'مكتمل'),
        ('cancelled', 'ملغي'),
        ('returned', 'مرتجع'),
    ]

    invoice_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='رقم الفاتورة'
    )
    customer = models.ForeignKey(
        'customers.Customer',
        on_delete=models.PROTECT,
        blank=True,
        null=True,
        related_name='sales',
        verbose_name='العميل'
    )
    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHODS,
        default='cash',
        verbose_name='طريقة الدفع'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='completed',
        verbose_name='الحالة'
    )
    subtotal = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name='المجموع الفرعي'
    )
    discount_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='مبلغ الخصم'
    )
    discount_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='نسبة الخصم'
    )
    tax_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='مبلغ الضريبة'
    )
    tax_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='نسبة الضريبة'
    )
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name='المبلغ الإجمالي'
    )
    paid_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name='المبلغ المدفوع'
    )
    change_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name='الباقي'
    )
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )
    cashier = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        verbose_name='الكاشير'
    )
    sale_date = models.DateTimeField(
        default=timezone.now,
        verbose_name='تاريخ البيع'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    class Meta:
        verbose_name = 'بيع'
        verbose_name_plural = 'المبيعات'
        ordering = ['-created_at']

    def __str__(self):
        return f"فاتورة {self.invoice_number} - {self.total_amount}"

    @property
    def remaining_amount(self):
        """المبلغ المتبقي"""
        return self.total_amount - self.paid_amount

    @property
    def is_fully_paid(self):
        """تحقق من الدفع الكامل"""
        return self.paid_amount >= self.total_amount

    def calculate_totals(self):
        """حساب المجاميع"""
        # حساب المجموع الفرعي
        self.subtotal = sum(item.total_price for item in self.items.all())

        # حساب الخصم
        if self.discount_percentage > 0:
            self.discount_amount = (self.subtotal * self.discount_percentage) / 100

        # حساب الضريبة
        taxable_amount = self.subtotal - self.discount_amount
        if self.tax_percentage > 0:
            self.tax_amount = (taxable_amount * self.tax_percentage) / 100

        # حساب المجموع الإجمالي
        self.total_amount = taxable_amount + self.tax_amount

        # حساب الباقي
        self.change_amount = max(0, self.paid_amount - self.total_amount)


class SaleItem(models.Model):
    """عناصر البيع"""

    sale = models.ForeignKey(
        Sale,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name='البيع'
    )
    product = models.ForeignKey(
        'products.Product',
        on_delete=models.PROTECT,
        verbose_name='المنتج'
    )
    quantity = models.IntegerField(
        validators=[MinValueValidator(1)],
        verbose_name='الكمية'
    )
    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name='سعر الوحدة'
    )
    total_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='السعر الإجمالي'
    )

    class Meta:
        verbose_name = 'عنصر بيع'
        verbose_name_plural = 'عناصر البيع'

    def __str__(self):
        return f"{self.product.name} - {self.quantity}"

    def save(self, *args, **kwargs):
        """حساب السعر الإجمالي وتحديث المخزون"""
        self.total_price = self.quantity * self.unit_price

        is_new = self.pk is None
        if is_new:
            # تقليل كمية المنتج من المخزون
            self.product.quantity -= self.quantity
            self.product.save()

        super().save(*args, **kwargs)


class SaleReturn(models.Model):
    """مرتجعات المبيعات"""

    original_sale = models.ForeignKey(
        Sale,
        on_delete=models.CASCADE,
        related_name='returns',
        verbose_name='البيع الأصلي'
    )
    return_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='رقم المرتجع'
    )
    return_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='مبلغ المرتجع'
    )
    reason = models.TextField(
        verbose_name='سبب المرتجع'
    )
    processed_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        verbose_name='معالج بواسطة'
    )
    return_date = models.DateTimeField(
        default=timezone.now,
        verbose_name='تاريخ المرتجع'
    )

    class Meta:
        verbose_name = 'مرتجع بيع'
        verbose_name_plural = 'مرتجعات المبيعات'
        ordering = ['-return_date']

    def __str__(self):
        return f"مرتجع {self.return_number} - {self.return_amount}"


class SaleReturnItem(models.Model):
    """عناصر مرتجع البيع"""

    sale_return = models.ForeignKey(
        SaleReturn,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name='المرتجع'
    )
    product = models.ForeignKey(
        'products.Product',
        on_delete=models.PROTECT,
        verbose_name='المنتج'
    )
    quantity = models.IntegerField(
        validators=[MinValueValidator(1)],
        verbose_name='الكمية'
    )
    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name='سعر الوحدة'
    )
    total_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='السعر الإجمالي'
    )

    class Meta:
        verbose_name = 'عنصر مرتجع بيع'
        verbose_name_plural = 'عناصر مرتجعات البيع'

    def __str__(self):
        return f"{self.product.name} - {self.quantity}"

    def save(self, *args, **kwargs):
        """حساب السعر الإجمالي وإرجاع المخزون"""
        self.total_price = self.quantity * self.unit_price

        is_new = self.pk is None
        if is_new:
            # إرجاع كمية المنتج للمخزون
            self.product.quantity += self.quantity
            self.product.save()

        super().save(*args, **kwargs)
