from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView
from django.urls import reverse_lazy
from django.contrib import messages
from django.db.models import Q, Sum

from .models import Supplier, PurchaseOrder, PurchaseOrderItem, SupplierTransaction
from .forms import SupplierForm, PurchaseOrderForm, SupplierTransactionForm


class SupplierListView(LoginRequiredMixin, ListView):
    """قائمة الموردين"""
    model = Supplier
    template_name = 'suppliers/supplier_list.html'
    context_object_name = 'suppliers'
    paginate_by = 20

    def get_queryset(self):
        queryset = Supplier.objects.filter(is_active=True).order_by('name')

        # البحث
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(company__icontains=search) |
                Q(phone__icontains=search) |
                Q(email__icontains=search)
            )

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['total_suppliers'] = self.get_queryset().count()
        context['total_balance'] = self.get_queryset().aggregate(
            total=Sum('current_balance')
        )['total'] or 0
        return context


class SupplierCreateView(LoginRequiredMixin, CreateView):
    """إنشاء مورد جديد"""
    model = Supplier
    form_class = SupplierForm
    template_name = 'suppliers/supplier_form.html'
    success_url = reverse_lazy('suppliers:supplier_list')

    def form_valid(self, form):
        messages.success(self.request, f'تم إنشاء المورد {form.instance.name} بنجاح')
        return super().form_valid(form)


class SupplierDetailView(LoginRequiredMixin, DetailView):
    """تفاصيل المورد"""
    model = Supplier
    template_name = 'suppliers/supplier_detail.html'
    context_object_name = 'supplier'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['transactions'] = SupplierTransaction.objects.filter(
            supplier=self.object
        ).select_related('created_by').order_by('-created_at')[:10]

        context['purchase_orders'] = PurchaseOrder.objects.filter(
            supplier=self.object
        ).order_by('-created_at')[:5]

        # إحصائيات المورد
        context['total_purchases'] = SupplierTransaction.objects.filter(
            supplier=self.object,
            transaction_type='purchase'
        ).aggregate(total=Sum('amount'))['total'] or 0

        context['total_payments'] = SupplierTransaction.objects.filter(
            supplier=self.object,
            transaction_type='payment'
        ).aggregate(total=Sum('amount'))['total'] or 0

        return context


class SupplierUpdateView(LoginRequiredMixin, UpdateView):
    """تحديث بيانات المورد"""
    model = Supplier
    form_class = SupplierForm
    template_name = 'suppliers/supplier_form.html'
    success_url = reverse_lazy('suppliers:supplier_list')

    def form_valid(self, form):
        messages.success(self.request, f'تم تحديث بيانات المورد {form.instance.name} بنجاح')
        return super().form_valid(form)


class SupplierDeleteView(LoginRequiredMixin, DeleteView):
    """حذف مورد"""
    model = Supplier
    template_name = 'suppliers/supplier_confirm_delete.html'
    success_url = reverse_lazy('suppliers:supplier_list')

    def delete(self, request, *args, **kwargs):
        supplier = self.get_object()
        supplier.is_active = False
        supplier.save()
        messages.success(request, f'تم إلغاء تفعيل المورد {supplier.name} بنجاح')
        return redirect(self.success_url)


class PurchaseOrderListView(LoginRequiredMixin, ListView):
    """قائمة أوامر الشراء"""
    model = PurchaseOrder
    template_name = 'suppliers/purchase_order_list.html'
    context_object_name = 'orders'
    paginate_by = 20

    def get_queryset(self):
        queryset = PurchaseOrder.objects.select_related('supplier', 'created_by').order_by('-created_at')

        # تصفية حسب المورد
        supplier = self.request.GET.get('supplier')
        if supplier:
            queryset = queryset.filter(supplier_id=supplier)

        # تصفية حسب الحالة
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['suppliers'] = Supplier.objects.filter(is_active=True)
        context['status_choices'] = PurchaseOrder.STATUS_CHOICES
        return context


class PurchaseOrderCreateView(LoginRequiredMixin, CreateView):
    """إنشاء أمر شراء جديد"""
    model = PurchaseOrder
    form_class = PurchaseOrderForm
    template_name = 'suppliers/purchase_order_form.html'
    success_url = reverse_lazy('suppliers:purchase_order_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user

        # توليد رقم أمر تلقائي
        last_order = PurchaseOrder.objects.order_by('-id').first()
        if last_order:
            order_number = f"PO{int(last_order.order_number[2:]) + 1:06d}"
        else:
            order_number = "PO000001"
        form.instance.order_number = order_number

        messages.success(self.request, f'تم إنشاء أمر الشراء {form.instance.order_number} بنجاح')
        return super().form_valid(form)


class PurchaseOrderDetailView(LoginRequiredMixin, DetailView):
    """تفاصيل أمر الشراء"""
    model = PurchaseOrder
    template_name = 'suppliers/purchase_order_detail.html'
    context_object_name = 'order'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['items'] = self.object.items.select_related('product')
        return context
