# Generated by Django 4.2.7 on 2025-07-10 13:07

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('products', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الأمر')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('confirmed', 'مؤكد'), ('received', 'مستلم'), ('cancelled', 'ملغي')], default='pending', max_length=20, verbose_name='الحالة')),
                ('order_date', models.DateField(default=django.utils.timezone.now, verbose_name='تاريخ الأمر')),
                ('expected_delivery', models.DateField(blank=True, null=True, verbose_name='تاريخ التسليم المتوقع')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='المبلغ الإجمالي')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
            ],
            options={
                'verbose_name': 'أمر شراء',
                'verbose_name_plural': 'أوامر الشراء',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المورد')),
                ('company', models.CharField(blank=True, max_length=200, null=True, verbose_name='اسم الشركة')),
                ('phone', models.CharField(blank=True, max_length=15, null=True, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('tax_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='الرقم الضريبي')),
                ('current_balance', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الرصيد الحالي')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'مورد',
                'verbose_name_plural': 'الموردين',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='SupplierTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('purchase', 'شراء'), ('payment', 'دفع'), ('return', 'مرتجع'), ('adjustment', 'تسوية')], max_length=20, verbose_name='نوع المعاملة')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('reference', models.CharField(blank=True, max_length=100, null=True, verbose_name='المرجع')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ المعاملة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='suppliers.supplier', verbose_name='المورد')),
            ],
            options={
                'verbose_name': 'معاملة مورد',
                'verbose_name_plural': 'معاملات الموردين',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='سعر الوحدة')),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر الإجمالي')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='products.product', verbose_name='المنتج')),
                ('purchase_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='suppliers.purchaseorder', verbose_name='أمر الشراء')),
            ],
            options={
                'verbose_name': 'عنصر أمر شراء',
                'verbose_name_plural': 'عناصر أوامر الشراء',
            },
        ),
        migrations.AddField(
            model_name='purchaseorder',
            name='supplier',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='purchase_orders', to='suppliers.supplier', verbose_name='المورد'),
        ),
    ]
