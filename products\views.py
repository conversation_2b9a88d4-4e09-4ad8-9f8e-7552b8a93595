from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.urls import reverse_lazy
from django.http import JsonResponse, HttpResponse
from django.contrib import messages
from django.db.models import Q, Sum, F
from django.contrib.auth.decorators import login_required
import io
import base64

from .models import Product, Category, StockMovement
from .forms import ProductForm, CategoryForm, StockMovementForm


class ProductListView(LoginRequiredMixin, ListView):
    """قائمة المنتجات"""
    model = Product
    template_name = 'products/product_list.html'
    context_object_name = 'products'
    paginate_by = 20

    def get_queryset(self):
        queryset = Product.objects.select_related('category').filter(is_active=True)

        # البحث
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(code__icontains=search) |
                Q(barcode__icontains=search)
            )

        # تصفية حسب التصنيف
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(category_id=category)

        # ترتيب
        order_by = self.request.GET.get('order_by', 'name')
        if order_by in ['name', 'code', 'selling_price', 'quantity', 'created_at']:
            queryset = queryset.order_by(order_by)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.filter(is_active=True)
        context['total_products'] = self.get_queryset().count()
        context['total_value'] = self.get_queryset().aggregate(
            total=Sum(F('quantity') * F('cost_price'))
        )['total'] or 0
        return context


class ProductCreateView(LoginRequiredMixin, CreateView):
    """إنشاء منتج جديد"""
    model = Product
    form_class = ProductForm
    template_name = 'products/product_form.html'
    success_url = reverse_lazy('products:product_list')

    def form_valid(self, form):
        messages.success(self.request, f'تم إنشاء المنتج {form.instance.name} بنجاح')
        return super().form_valid(form)


class ProductDetailView(LoginRequiredMixin, DetailView):
    """تفاصيل المنتج"""
    model = Product
    template_name = 'products/product_detail.html'
    context_object_name = 'product'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['stock_movements'] = StockMovement.objects.filter(
            product=self.object
        ).select_related('created_by').order_by('-created_at')[:10]
        return context


class ProductUpdateView(LoginRequiredMixin, UpdateView):
    """تحديث بيانات المنتج"""
    model = Product
    form_class = ProductForm
    template_name = 'products/product_form.html'
    success_url = reverse_lazy('products:product_list')

    def form_valid(self, form):
        messages.success(self.request, f'تم تحديث المنتج {form.instance.name} بنجاح')
        return super().form_valid(form)


class ProductDeleteView(LoginRequiredMixin, DeleteView):
    """حذف منتج"""
    model = Product
    template_name = 'products/product_confirm_delete.html'
    success_url = reverse_lazy('products:product_list')

    def delete(self, request, *args, **kwargs):
        product = self.get_object()
        product.is_active = False
        product.save()
        messages.success(request, f'تم إلغاء تفعيل المنتج {product.name} بنجاح')
        return redirect(self.success_url)


class CategoryListView(LoginRequiredMixin, ListView):
    """قائمة التصنيفات"""
    model = Category
    template_name = 'products/category_list.html'
    context_object_name = 'categories'

    def get_queryset(self):
        return Category.objects.filter(is_active=True).order_by('name')


class CategoryCreateView(LoginRequiredMixin, CreateView):
    """إنشاء تصنيف جديد"""
    model = Category
    form_class = CategoryForm
    template_name = 'products/category_form.html'
    success_url = reverse_lazy('products:category_list')

    def form_valid(self, form):
        messages.success(self.request, f'تم إنشاء التصنيف {form.instance.name} بنجاح')
        return super().form_valid(form)


class CategoryUpdateView(LoginRequiredMixin, UpdateView):
    """تحديث بيانات التصنيف"""
    model = Category
    form_class = CategoryForm
    template_name = 'products/category_form.html'
    success_url = reverse_lazy('products:category_list')

    def form_valid(self, form):
        messages.success(self.request, f'تم تحديث التصنيف {form.instance.name} بنجاح')
        return super().form_valid(form)


class StockMovementListView(LoginRequiredMixin, ListView):
    """قائمة حركات المخزون"""
    model = StockMovement
    template_name = 'products/stock_movement_list.html'
    context_object_name = 'movements'
    paginate_by = 50

    def get_queryset(self):
        queryset = StockMovement.objects.select_related(
            'product', 'created_by'
        ).order_by('-created_at')

        # تصفية حسب المنتج
        product = self.request.GET.get('product')
        if product:
            queryset = queryset.filter(product_id=product)

        # تصفية حسب نوع الحركة
        movement_type = self.request.GET.get('movement_type')
        if movement_type:
            queryset = queryset.filter(movement_type=movement_type)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['products'] = Product.objects.filter(is_active=True)
        context['movement_types'] = StockMovement.MOVEMENT_TYPES
        return context


class StockMovementCreateView(LoginRequiredMixin, CreateView):
    """إنشاء حركة مخزون جديدة"""
    model = StockMovement
    form_class = StockMovementForm
    template_name = 'products/stock_movement_form.html'
    success_url = reverse_lazy('products:stock_movement_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, 'تم إنشاء حركة المخزون بنجاح')
        return super().form_valid(form)


class LowStockView(LoginRequiredMixin, ListView):
    """المنتجات منخفضة المخزون"""
    model = Product
    template_name = 'products/low_stock.html'
    context_object_name = 'products'

    def get_queryset(self):
        return Product.objects.filter(
            is_active=True,
            quantity__lte=F('min_quantity')
        ).select_related('category').order_by('quantity')


@login_required
def generate_barcode(request, pk):
    """توليد باركود للمنتج"""
    product = get_object_or_404(Product, pk=pk)

    try:
        # توليد الباركود
        barcode_data = product.generate_barcode()

        # إرجاع الباركود كصورة
        response = HttpResponse(barcode_data, content_type='image/png')
        response['Content-Disposition'] = f'attachment; filename="{product.code}_barcode.png"'
        return response

    except Exception as e:
        messages.error(request, f'خطأ في توليد الباركود: {str(e)}')
        return redirect('products:product_detail', pk=pk)
