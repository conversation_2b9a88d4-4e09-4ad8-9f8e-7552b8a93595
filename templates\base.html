<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}مكتبة سنتر آدم{% endblock %}</title>
    
    <!-- Bootstrap 5 RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(-5px);
        }
        
        .main-content {
            padding: 20px;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .navbar {
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 8px;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">مكتبة سنتر آدم</h4>
                        <small class="text-white-50">نظام إدارة متكامل</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'dashboard:home' %}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        
                        {% if user.can_process_sales %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'sales:pos' %}">
                                <i class="fas fa-cash-register me-2"></i>
                                نقطة البيع
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'sales:sale_list' %}">
                                <i class="fas fa-receipt me-2"></i>
                                المبيعات
                            </a>
                        </li>
                        {% endif %}
                        
                        {% if user.can_manage_inventory %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'products:product_list' %}">
                                <i class="fas fa-boxes me-2"></i>
                                المنتجات
                            </a>
                        </li>
                        {% endif %}
                        
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'customers:customer_list' %}">
                                <i class="fas fa-users me-2"></i>
                                العملاء
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'suppliers:supplier_list' %}">
                                <i class="fas fa-truck me-2"></i>
                                الموردين
                            </a>
                        </li>
                        
                        {% if user.can_access_accounting %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'accounting:dashboard' %}">
                                <i class="fas fa-calculator me-2"></i>
                                المحاسبة
                            </a>
                        </li>
                        {% endif %}
                        
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'reports:dashboard' %}">
                                <i class="fas fa-chart-bar me-2"></i>
                                التقارير
                            </a>
                        </li>
                        
                        {% if user.can_manage_users %}
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'employees:employee_list' %}">
                                <i class="fas fa-user-tie me-2"></i>
                                الموظفين
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'settings_app:settings' %}">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </nav>
            
            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- Top navbar -->
                <nav class="navbar navbar-expand-lg navbar-light bg-white mb-4">
                    <div class="container-fluid">
                        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                            <span class="navbar-toggler-icon"></span>
                        </button>
                        
                        <div class="collapse navbar-collapse" id="navbarNav">
                            <ul class="navbar-nav me-auto">
                                <li class="nav-item">
                                    <span class="navbar-text">
                                        <i class="fas fa-calendar me-1"></i>
                                        {{ "now"|date:"Y/m/d" }}
                                    </span>
                                </li>
                            </ul>
                            
                            <ul class="navbar-nav">
                                <li class="nav-item dropdown">
                                    <a class="nav-link position-relative" href="#" id="notificationDropdown" role="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-bell"></i>
                                        <span class="notification-badge">3</span>
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><h6 class="dropdown-header">التنبيهات</h6></li>
                                        <li><a class="dropdown-item" href="#">تنبيه جديد</a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="{% url 'dashboard:notifications' %}">عرض جميع التنبيهات</a></li>
                                    </ul>
                                </li>
                                
                                <li class="nav-item dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                                        <i class="fas fa-user-circle me-1"></i>
                                        {{ user.get_full_name }}
                                    </a>
                                    <ul class="dropdown-menu dropdown-menu-end">
                                        <li><a class="dropdown-item" href="{% url 'authentication:profile' %}">
                                            <i class="fas fa-user me-2"></i>الملف الشخصي
                                        </a></li>
                                        <li><a class="dropdown-item" href="{% url 'authentication:change_password' %}">
                                            <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="{% url 'authentication:logout' %}">
                                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                                        </a></li>
                                    </ul>
                                </li>
                            </ul>
                        </div>
                    </div>
                </nav>
                
                <!-- Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
                
                <!-- Page content -->
                <div class="main-content">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
