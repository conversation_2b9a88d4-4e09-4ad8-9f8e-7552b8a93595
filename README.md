# مكتبة سنتر آدم - نظام إدارة متكامل للمكتبات

## 📚 نظرة عامة

نظام إدارة متكامل للمكتبات مطور بـ Django يوفر جميع الوظائف المطلوبة لإدارة مكتبة حديثة بما في ذلك:

- 🔐 نظام مصادقة وصلاحيات متقدم
- 💰 نقطة بيع سريعة وسهلة الاستخدام
- 📦 إدارة شاملة للمنتجات والمخزون
- 👥 إدارة العملاء والموردين
- 📊 نظام محاسبة متكامل
- 📈 تقارير مفصلة وإحصائيات
- ⚙️ إعدادات قابلة للتخصيص

## 🏗️ هيكل المشروع

```
sentr_adam/
├── authentication/     # نظام المصادقة والمستخدمين
├── dashboard/          # لوحة التحكم الرئيسية
├── sales/             # نظام المبيعات ونقاط البيع
├── products/          # إدارة المنتجات والمخزون
├── customers/         # إدارة العملاء
├── suppliers/         # إدارة الموردين
├── accounting/        # النظام المحاسبي
├── employees/         # إدارة الموظفين
├── reports/           # التقارير والإحصائيات
├── settings_app/      # إعدادات النظام
├── templates/         # قوالب HTML
├── static/           # الملفات الثابتة
└── media/            # ملفات الوسائط
```

## 🚀 التثبيت والتشغيل

### 1. متطلبات النظام

- Python 3.11+
- pip (مدير حزم Python)

### 2. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 3. إعداد قاعدة البيانات

```bash
python manage.py makemigrations
python manage.py migrate
```

### 4. إنشاء مستخدم مدير

```bash
python manage.py createsuperuser
```

### 5. تشغيل الخادم

```bash
python manage.py runserver
```

الآن يمكنك الوصول للنظام عبر: http://127.0.0.1:8000

## 👤 أدوار المستخدمين

### مدير (Admin)
- الوصول الكامل لجميع أجزاء النظام
- إدارة المستخدمين والصلاحيات
- عرض جميع التقارير والإحصائيات
- إعدادات النظام

### كاشير (Cashier)
- نقطة البيع
- عرض المنتجات
- إدارة العملاء الأساسية

### محاسب (Accountant)
- النظام المحاسبي
- التقارير المالية
- إدارة الخزينة

### مشرف مخازن (Warehouse Manager)
- إدارة المنتجات والمخزون
- حركات المخزون
- إدارة الموردين

## 🎨 الواجهات

### لوحة التحكم
- إحصائيات سريعة للمبيعات اليومية
- مؤشرات المخزون والتنبيهات
- أحدث المبيعات والمنتجات الأكثر مبيعاً

### نقطة البيع
- واجهة سريعة وسهلة الاستخدام
- دعم البحث والباركود
- حساب تلقائي للخصومات والضرائب
- طرق دفع متعددة

### إدارة المنتجات
- إضافة وتعديل المنتجات
- تصنيفات هرمية
- تتبع المخزون والتنبيهات
- توليد الباركود

## 📊 التقارير

- تقارير المبيعات (يومية/شهرية/سنوية)
- تقارير المخزون والمنتجات
- تقارير العملاء والموردين
- التقارير المحاسبية والمالية
- تصدير PDF وExcel

## 🔧 الإعدادات

- معلومات الشركة والشعار
- إعدادات الضرائب والعملة
- تخصيص الألوان والمظهر
- النسخ الاحتياطي التلقائي
- إدارة الفروع

## 🛡️ الأمان

- تشفير كلمات المرور
- حماية CSRF
- تسجيل النشاطات
- جلسات آمنة
- صلاحيات متدرجة

## 📱 التصميم المتجاوب

النظام مصمم ليعمل بكفاءة على:
- أجهزة الكمبيوتر المكتبية
- الأجهزة اللوحية
- الهواتف الذكية

## 🌐 دعم اللغة العربية

- واجهة عربية كاملة (RTL)
- خطوط عربية جميلة
- تنسيق التواريخ والأرقام
- دعم ثنائي اللغة (عربي/إنجليزي)

## 📋 المهام المكتملة

- ✅ إعداد البيئة والمشروع الأساسي
- ✅ نظام المصادقة والصلاحيات
- ✅ نماذج البيانات الأساسية
- ✅ واجهات المستخدم الأساسية
- ✅ نقطة البيع
- ✅ إدارة المنتجات والمخزون
- ✅ إدارة العملاء والموردين

## 🔄 المهام المتبقية

- ⏳ إكمال النظام المحاسبي
- ⏳ نظام التقارير المتقدم
- ⏳ لوحة التحكم التفاعلية
- ⏳ إدارة الموظفين والرواتب
- ⏳ الإعدادات المتقدمة
- ⏳ اختبار النظام وإصلاح الأخطاء

## 🚀 للمطورين

### إضافة ميزة جديدة

1. إنشاء تطبيق Django جديد
2. تعريف النماذج في `models.py`
3. إنشاء النماذج في `forms.py`
4. كتابة العروض في `views.py`
5. إضافة URLs في `urls.py`
6. تصميم القوالب في `templates/`

### قاعدة البيانات

النظام يستخدم SQLite للتطوير ويمكن تغييره لـ MySQL أو PostgreSQL للإنتاج.

### الاستضافة

النظام جاهز للاستضافة على:
- PythonAnywhere
- Heroku
- DigitalOcean
- AWS

## 📞 الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

---

**مكتبة سنتر آدم** - نظام إدارة متكامل ومتطور للمكتبات الحديثة 📚✨
