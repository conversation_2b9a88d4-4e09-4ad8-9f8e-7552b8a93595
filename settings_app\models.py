from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator


class SystemSettings(models.Model):
    """إعدادات النظام"""

    # معلومات الشركة
    company_name = models.CharField(
        max_length=200,
        default='مكتبة سنتر آدم',
        verbose_name='اسم الشركة'
    )
    company_address = models.TextField(
        blank=True,
        null=True,
        verbose_name='عنوان الشركة'
    )
    company_phone = models.CharField(
        max_length=15,
        blank=True,
        null=True,
        verbose_name='هاتف الشركة'
    )
    company_email = models.EmailField(
        blank=True,
        null=True,
        verbose_name='بريد الشركة'
    )
    company_logo = models.ImageField(
        upload_to='company/',
        blank=True,
        null=True,
        verbose_name='شعار الشركة'
    )
    tax_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='الرقم الضريبي'
    )

    # إعدادات الضرائب
    tax_enabled = models.BooleanField(
        default=False,
        verbose_name='تفعيل الضرائب'
    )
    tax_rate = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        verbose_name='معدل الضريبة'
    )

    # إعدادات العملة
    currency_code = models.CharField(
        max_length=3,
        default='EGP',
        verbose_name='رمز العملة'
    )
    currency_symbol = models.CharField(
        max_length=5,
        default='ج.م',
        verbose_name='رمز العملة'
    )

    # إعدادات الفواتير
    invoice_prefix = models.CharField(
        max_length=10,
        default='INV',
        verbose_name='بادئة الفاتورة'
    )
    invoice_start_number = models.IntegerField(
        default=1,
        verbose_name='رقم بداية الفاتورة'
    )

    # إعدادات النظام
    language = models.CharField(
        max_length=5,
        choices=[('ar', 'العربية'), ('en', 'English')],
        default='ar',
        verbose_name='اللغة'
    )
    theme = models.CharField(
        max_length=10,
        choices=[('light', 'فاتح'), ('dark', 'داكن')],
        default='light',
        verbose_name='المظهر'
    )

    # إعدادات النسخ الاحتياطي
    backup_enabled = models.BooleanField(
        default=True,
        verbose_name='تفعيل النسخ الاحتياطي'
    )
    backup_frequency = models.CharField(
        max_length=10,
        choices=[
            ('daily', 'يومي'),
            ('weekly', 'أسبوعي'),
            ('monthly', 'شهري')
        ],
        default='daily',
        verbose_name='تكرار النسخ الاحتياطي'
    )

    # إعدادات التنبيهات
    low_stock_alert = models.BooleanField(
        default=True,
        verbose_name='تنبيه انخفاض المخزون'
    )
    email_notifications = models.BooleanField(
        default=False,
        verbose_name='تنبيهات البريد الإلكتروني'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    class Meta:
        verbose_name = 'إعدادات النظام'
        verbose_name_plural = 'إعدادات النظام'

    def __str__(self):
        return f"إعدادات {self.company_name}"

    def save(self, *args, **kwargs):
        # التأكد من وجود سجل واحد فقط
        if not self.pk and SystemSettings.objects.exists():
            raise ValueError('يمكن وجود سجل واحد فقط من إعدادات النظام')
        super().save(*args, **kwargs)

    @classmethod
    def get_settings(cls):
        """الحصول على إعدادات النظام"""
        settings, created = cls.objects.get_or_create(pk=1)
        return settings


class Branch(models.Model):
    """الفروع"""

    name = models.CharField(
        max_length=100,
        verbose_name='اسم الفرع'
    )
    address = models.TextField(
        verbose_name='العنوان'
    )
    phone = models.CharField(
        max_length=15,
        blank=True,
        null=True,
        verbose_name='الهاتف'
    )
    manager = models.ForeignKey(
        'authentication.User',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='managed_branches',
        verbose_name='مدير الفرع'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    class Meta:
        verbose_name = 'فرع'
        verbose_name_plural = 'الفروع'
        ordering = ['name']

    def __str__(self):
        return self.name


class Notification(models.Model):
    """التنبيهات"""

    NOTIFICATION_TYPES = [
        ('info', 'معلومات'),
        ('warning', 'تحذير'),
        ('error', 'خطأ'),
        ('success', 'نجاح'),
    ]

    title = models.CharField(
        max_length=200,
        verbose_name='العنوان'
    )
    message = models.TextField(
        verbose_name='الرسالة'
    )
    notification_type = models.CharField(
        max_length=10,
        choices=NOTIFICATION_TYPES,
        default='info',
        verbose_name='نوع التنبيه'
    )
    user = models.ForeignKey(
        'authentication.User',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='notifications',
        verbose_name='المستخدم'
    )
    is_read = models.BooleanField(
        default=False,
        verbose_name='مقروء'
    )
    is_global = models.BooleanField(
        default=False,
        verbose_name='عام لجميع المستخدمين'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    class Meta:
        verbose_name = 'تنبيه'
        verbose_name_plural = 'التنبيهات'
        ordering = ['-created_at']

    def __str__(self):
        return self.title


class BackupLog(models.Model):
    """سجل النسخ الاحتياطي"""

    STATUS_CHOICES = [
        ('success', 'نجح'),
        ('failed', 'فشل'),
        ('in_progress', 'قيد التنفيذ'),
    ]

    filename = models.CharField(
        max_length=200,
        verbose_name='اسم الملف'
    )
    file_size = models.BigIntegerField(
        default=0,
        verbose_name='حجم الملف'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        verbose_name='الحالة'
    )
    error_message = models.TextField(
        blank=True,
        null=True,
        verbose_name='رسالة الخطأ'
    )
    created_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        verbose_name='أنشأ بواسطة'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    class Meta:
        verbose_name = 'سجل نسخة احتياطية'
        verbose_name_plural = 'سجل النسخ الاحتياطية'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.filename} - {self.get_status_display()}"
