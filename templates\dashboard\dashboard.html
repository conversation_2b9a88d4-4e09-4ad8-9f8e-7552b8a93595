{% extends 'base.html' %}
{% load humanize %}

{% block title %}لوحة التحكم - مكتبة سنتر آدم{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">لوحة التحكم</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download me-1"></i>تصدير
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            مبيعات اليوم
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ today_sales.total|default:0|floatformat:2 }} ج.م
                        </div>
                        <div class="small">
                            {{ today_sales.count|default:0 }} فاتورة
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-cash-register fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            إجمالي المنتجات
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ total_products|default:0 }}
                        </div>
                        <div class="small text-warning">
                            {{ low_stock_products|default:0 }} منتج ينفد
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-boxes fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            رصيد الخزينة
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ cash_balance|floatformat:2 }} ج.م
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-wallet fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">
                            العملاء
                        </div>
                        <div class="h5 mb-0 font-weight-bold">
                            {{ total_customers|default:0 }}
                        </div>
                        <div class="small">
                            {{ total_suppliers|default:0 }} مورد
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- أحدث المبيعات -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-receipt me-2"></i>أحدث المبيعات
                </h5>
                <a href="{% url 'sales:sale_list' %}" class="btn btn-sm btn-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% if recent_sales %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>العميل</th>
                                    <th>المبلغ</th>
                                    <th>الكاشير</th>
                                    <th>التاريخ</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sale in recent_sales %}
                                <tr>
                                    <td>
                                        <a href="{% url 'sales:sale_detail' sale.pk %}" class="text-decoration-none">
                                            {{ sale.invoice_number }}
                                        </a>
                                    </td>
                                    <td>{{ sale.customer.name|default:"عميل نقدي" }}</td>
                                    <td>{{ sale.total_amount|floatformat:2 }} ج.م</td>
                                    <td>{{ sale.cashier.get_full_name }}</td>
                                    <td>{{ sale.created_at|date:"Y/m/d H:i" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد مبيعات حتى الآن</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- المنتجات الأكثر مبيعاً -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-star me-2"></i>الأكثر مبيعاً
                </h5>
            </div>
            <div class="card-body">
                {% if top_products %}
                    {% for product in top_products %}
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <h6 class="mb-1">{{ product.product__name }}</h6>
                            <small class="text-muted">{{ product.total_quantity }} قطعة</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-primary">
                                {{ product.total_revenue|floatformat:0 }} ج.م
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد بيانات</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- التنبيهات -->
{% if notifications %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bell me-2"></i>التنبيهات
                </h5>
                <a href="{% url 'dashboard:notifications' %}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                {% for notification in notifications %}
                <div class="alert alert-{{ notification.notification_type }} alert-dismissible fade show" role="alert">
                    <strong>{{ notification.title }}</strong>
                    <p class="mb-0">{{ notification.message }}</p>
                    <small class="text-muted">{{ notification.created_at|timesince }}</small>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
    // رسم بياني للمبيعات (يمكن إضافته لاحقاً)
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة أي JavaScript مطلوب للوحة التحكم
        console.log('Dashboard loaded successfully');
    });
</script>
{% endblock %}
