from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone


class Supplier(models.Model):
    """الموردين"""

    name = models.CharField(
        max_length=200,
        verbose_name='اسم المورد'
    )
    company = models.CharField(
        max_length=200,
        blank=True,
        null=True,
        verbose_name='اسم الشركة'
    )
    phone = models.CharField(
        max_length=15,
        blank=True,
        null=True,
        verbose_name='رقم الهاتف'
    )
    email = models.EmailField(
        blank=True,
        null=True,
        verbose_name='البريد الإلكتروني'
    )
    address = models.TextField(
        blank=True,
        null=True,
        verbose_name='العنوان'
    )
    tax_number = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        verbose_name='الرقم الضريبي'
    )
    current_balance = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name='الرصيد الحالي'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    class Meta:
        verbose_name = 'مورد'
        verbose_name_plural = 'الموردين'
        ordering = ['name']

    def __str__(self):
        return self.name


class PurchaseOrder(models.Model):
    """أوامر الشراء"""

    STATUS_CHOICES = [
        ('pending', 'معلق'),
        ('confirmed', 'مؤكد'),
        ('received', 'مستلم'),
        ('cancelled', 'ملغي'),
    ]

    order_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='رقم الأمر'
    )
    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.PROTECT,
        related_name='purchase_orders',
        verbose_name='المورد'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name='الحالة'
    )
    order_date = models.DateField(
        default=timezone.now,
        verbose_name='تاريخ الأمر'
    )
    expected_delivery = models.DateField(
        blank=True,
        null=True,
        verbose_name='تاريخ التسليم المتوقع'
    )
    total_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name='المبلغ الإجمالي'
    )
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )
    created_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        verbose_name='أنشأ بواسطة'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    class Meta:
        verbose_name = 'أمر شراء'
        verbose_name_plural = 'أوامر الشراء'
        ordering = ['-created_at']

    def __str__(self):
        return f"أمر شراء {self.order_number} - {self.supplier.name}"


class PurchaseOrderItem(models.Model):
    """عناصر أمر الشراء"""

    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.CASCADE,
        related_name='items',
        verbose_name='أمر الشراء'
    )
    product = models.ForeignKey(
        'products.Product',
        on_delete=models.PROTECT,
        verbose_name='المنتج'
    )
    quantity = models.IntegerField(
        validators=[MinValueValidator(1)],
        verbose_name='الكمية'
    )
    unit_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name='سعر الوحدة'
    )
    total_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='السعر الإجمالي'
    )

    class Meta:
        verbose_name = 'عنصر أمر شراء'
        verbose_name_plural = 'عناصر أوامر الشراء'

    def __str__(self):
        return f"{self.product.name} - {self.quantity}"

    def save(self, *args, **kwargs):
        """حساب السعر الإجمالي"""
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)


class SupplierTransaction(models.Model):
    """معاملات الموردين"""

    TRANSACTION_TYPES = [
        ('purchase', 'شراء'),
        ('payment', 'دفع'),
        ('return', 'مرتجع'),
        ('adjustment', 'تسوية'),
    ]

    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.CASCADE,
        related_name='transactions',
        verbose_name='المورد'
    )
    transaction_type = models.CharField(
        max_length=20,
        choices=TRANSACTION_TYPES,
        verbose_name='نوع المعاملة'
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='المبلغ'
    )
    reference = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='المرجع'
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name='الوصف'
    )
    created_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        verbose_name='أنشأ بواسطة'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ المعاملة'
    )

    class Meta:
        verbose_name = 'معاملة مورد'
        verbose_name_plural = 'معاملات الموردين'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.supplier.name} - {self.get_transaction_type_display()} - {self.amount}"

    def save(self, *args, **kwargs):
        """تحديث رصيد المورد عند حفظ المعاملة"""
        is_new = self.pk is None

        if is_new:
            # تحديث رصيد المورد
            if self.transaction_type in ['purchase']:
                self.supplier.current_balance += self.amount
            elif self.transaction_type in ['payment', 'return']:
                self.supplier.current_balance -= self.amount
            elif self.transaction_type == 'adjustment':
                # التسوية يمكن أن تكون موجبة أو سالبة
                self.supplier.current_balance += self.amount

            self.supplier.save()

        super().save(*args, **kwargs)
