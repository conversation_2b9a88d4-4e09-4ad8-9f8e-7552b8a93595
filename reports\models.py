from django.db import models
from django.utils import timezone


class ReportTemplate(models.Model):
    """قوالب التقارير"""

    REPORT_TYPES = [
        ('sales', 'تقارير المبيعات'),
        ('inventory', 'تقارير المخزون'),
        ('financial', 'التقارير المالية'),
        ('customer', 'تقارير العملاء'),
        ('supplier', 'تقارير الموردين'),
        ('employee', 'تقارير الموظفين'),
    ]

    name = models.CharField(
        max_length=200,
        verbose_name='اسم التقرير'
    )
    report_type = models.CharField(
        max_length=20,
        choices=REPORT_TYPES,
        verbose_name='نوع التقرير'
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name='الوصف'
    )
    sql_query = models.TextField(
        verbose_name='استعلام SQL'
    )
    parameters = models.JSONField(
        default=dict,
        blank=True,
        verbose_name='المعاملات'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )
    created_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        verbose_name='أنشأ بواسطة'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    class Meta:
        verbose_name = 'قالب تقرير'
        verbose_name_plural = 'قوالب التقارير'
        ordering = ['report_type', 'name']

    def __str__(self):
        return f"{self.get_report_type_display()} - {self.name}"


class GeneratedReport(models.Model):
    """التقارير المُولدة"""

    FORMAT_CHOICES = [
        ('pdf', 'PDF'),
        ('excel', 'Excel'),
        ('csv', 'CSV'),
    ]

    STATUS_CHOICES = [
        ('generating', 'قيد التوليد'),
        ('completed', 'مكتمل'),
        ('failed', 'فشل'),
    ]

    template = models.ForeignKey(
        ReportTemplate,
        on_delete=models.CASCADE,
        related_name='generated_reports',
        verbose_name='قالب التقرير'
    )
    name = models.CharField(
        max_length=200,
        verbose_name='اسم التقرير'
    )
    format = models.CharField(
        max_length=10,
        choices=FORMAT_CHOICES,
        verbose_name='التنسيق'
    )
    parameters = models.JSONField(
        default=dict,
        verbose_name='المعاملات المستخدمة'
    )
    file_path = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        verbose_name='مسار الملف'
    )
    file_size = models.BigIntegerField(
        default=0,
        verbose_name='حجم الملف'
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='generating',
        verbose_name='الحالة'
    )
    error_message = models.TextField(
        blank=True,
        null=True,
        verbose_name='رسالة الخطأ'
    )
    generated_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        verbose_name='ولد بواسطة'
    )
    generated_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ التوليد'
    )

    class Meta:
        verbose_name = 'تقرير مُولد'
        verbose_name_plural = 'التقارير المُولدة'
        ordering = ['-generated_at']

    def __str__(self):
        return f"{self.name} - {self.get_format_display()}"


class ReportSchedule(models.Model):
    """جدولة التقارير"""

    FREQUENCY_CHOICES = [
        ('daily', 'يومي'),
        ('weekly', 'أسبوعي'),
        ('monthly', 'شهري'),
        ('quarterly', 'ربع سنوي'),
        ('yearly', 'سنوي'),
    ]

    template = models.ForeignKey(
        ReportTemplate,
        on_delete=models.CASCADE,
        related_name='schedules',
        verbose_name='قالب التقرير'
    )
    name = models.CharField(
        max_length=200,
        verbose_name='اسم الجدولة'
    )
    frequency = models.CharField(
        max_length=20,
        choices=FREQUENCY_CHOICES,
        verbose_name='التكرار'
    )
    parameters = models.JSONField(
        default=dict,
        verbose_name='المعاملات'
    )
    recipients = models.JSONField(
        default=list,
        verbose_name='المستلمين'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )
    last_run = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='آخر تشغيل'
    )
    next_run = models.DateTimeField(
        verbose_name='التشغيل التالي'
    )
    created_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        verbose_name='أنشأ بواسطة'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    class Meta:
        verbose_name = 'جدولة تقرير'
        verbose_name_plural = 'جدولة التقارير'
        ordering = ['next_run']

    def __str__(self):
        return f"{self.name} - {self.get_frequency_display()}"
