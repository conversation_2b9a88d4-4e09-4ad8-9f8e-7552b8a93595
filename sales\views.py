from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.urls import reverse_lazy
from django.http import JsonResponse, HttpResponse
from django.contrib import messages
from django.db.models import Q, Sum
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
import json
from datetime import datetime

from .models import Sale, SaleItem, SaleReturn, SaleReturnItem
from products.models import Product
from customers.models import Customer
from .forms import SaleForm, SaleItemFormSet, SaleReturnForm


class SaleListView(LoginRequiredMixin, ListView):
    """قائمة المبيعات"""
    model = Sale
    template_name = 'sales/sale_list.html'
    context_object_name = 'sales'
    paginate_by = 20

    def get_queryset(self):
        queryset = Sale.objects.select_related('customer', 'cashier').order_by('-created_at')

        # البحث
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(invoice_number__icontains=search) |
                Q(customer__name__icontains=search)
            )

        # تصفية حسب التاريخ
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')

        if date_from:
            queryset = queryset.filter(sale_date__date__gte=date_from)
        if date_to:
            queryset = queryset.filter(sale_date__date__lte=date_to)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['total_sales'] = self.get_queryset().aggregate(
            total=Sum('total_amount')
        )['total'] or 0
        return context


class POSView(LoginRequiredMixin, TemplateView):
    """واجهة نقطة البيع"""
    template_name = 'sales/pos.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['products'] = Product.objects.filter(
            is_active=True,
            quantity__gt=0
        ).select_related('category')[:20]
        context['customers'] = Customer.objects.filter(is_active=True)[:10]
        return context


class SaleCreateView(LoginRequiredMixin, CreateView):
    """إنشاء بيع جديد"""
    model = Sale
    form_class = SaleForm
    template_name = 'sales/sale_form.html'
    success_url = reverse_lazy('sales:sale_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.POST:
            context['formset'] = SaleItemFormSet(self.request.POST)
        else:
            context['formset'] = SaleItemFormSet()
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']

        if formset.is_valid():
            form.instance.cashier = self.request.user
            # توليد رقم فاتورة تلقائي
            last_sale = Sale.objects.order_by('-id').first()
            if last_sale:
                invoice_number = f"INV{int(last_sale.invoice_number[3:]) + 1:06d}"
            else:
                invoice_number = "INV000001"
            form.instance.invoice_number = invoice_number

            self.object = form.save()
            formset.instance = self.object
            formset.save()

            # حساب المجاميع
            self.object.calculate_totals()
            self.object.save()

            messages.success(self.request, f'تم إنشاء الفاتورة {self.object.invoice_number} بنجاح')
            return super().form_valid(form)
        else:
            return self.render_to_response(self.get_context_data(form=form))


class SaleDetailView(LoginRequiredMixin, DetailView):
    """تفاصيل البيع"""
    model = Sale
    template_name = 'sales/sale_detail.html'
    context_object_name = 'sale'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['items'] = self.object.items.select_related('product')
        return context


class SaleUpdateView(LoginRequiredMixin, UpdateView):
    """تحديث بيانات البيع"""
    model = Sale
    form_class = SaleForm
    template_name = 'sales/sale_form.html'
    success_url = reverse_lazy('sales:sale_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if self.request.POST:
            context['formset'] = SaleItemFormSet(self.request.POST, instance=self.object)
        else:
            context['formset'] = SaleItemFormSet(instance=self.object)
        return context

    def form_valid(self, form):
        context = self.get_context_data()
        formset = context['formset']

        if formset.is_valid():
            self.object = form.save()
            formset.instance = self.object
            formset.save()

            # إعادة حساب المجاميع
            self.object.calculate_totals()
            self.object.save()

            messages.success(self.request, f'تم تحديث الفاتورة {self.object.invoice_number} بنجاح')
            return super().form_valid(form)
        else:
            return self.render_to_response(self.get_context_data(form=form))


class SaleDeleteView(LoginRequiredMixin, DeleteView):
    """حذف بيع"""
    model = Sale
    template_name = 'sales/sale_confirm_delete.html'
    success_url = reverse_lazy('sales:sale_list')

    def delete(self, request, *args, **kwargs):
        sale = self.get_object()
        messages.success(request, f'تم حذف الفاتورة {sale.invoice_number} بنجاح')
        return super().delete(request, *args, **kwargs)


class SaleReturnListView(LoginRequiredMixin, ListView):
    """قائمة مرتجعات المبيعات"""
    model = SaleReturn
    template_name = 'sales/return_list.html'
    context_object_name = 'returns'
    paginate_by = 20

    def get_queryset(self):
        return SaleReturn.objects.select_related(
            'original_sale', 'processed_by'
        ).order_by('-return_date')


class SaleReturnCreateView(LoginRequiredMixin, CreateView):
    """إنشاء مرتجع جديد"""
    model = SaleReturn
    form_class = SaleReturnForm
    template_name = 'sales/return_form.html'
    success_url = reverse_lazy('sales:return_list')

    def form_valid(self, form):
        form.instance.processed_by = self.request.user
        # توليد رقم مرتجع تلقائي
        last_return = SaleReturn.objects.order_by('-id').first()
        if last_return:
            return_number = f"RET{int(last_return.return_number[3:]) + 1:06d}"
        else:
            return_number = "RET000001"
        form.instance.return_number = return_number

        messages.success(self.request, f'تم إنشاء المرتجع {form.instance.return_number} بنجاح')
        return super().form_valid(form)


@login_required
def print_invoice(request, pk):
    """طباعة الفاتورة"""
    sale = get_object_or_404(Sale, pk=pk)

    # هنا يمكن إضافة منطق طباعة الفاتورة
    # يمكن استخدام مكتبة مثل ReportLab لتوليد PDF

    return render(request, 'sales/invoice_print.html', {
        'sale': sale,
        'items': sale.items.select_related('product')
    })


@csrf_exempt
@login_required
def product_search_api(request):
    """API للبحث عن المنتجات"""
    if request.method == 'GET':
        query = request.GET.get('q', '')

        products = Product.objects.filter(
            Q(name__icontains=query) |
            Q(code__icontains=query) |
            Q(barcode__icontains=query),
            is_active=True,
            quantity__gt=0
        )[:10]

        data = []
        for product in products:
            data.append({
                'id': product.id,
                'name': product.name,
                'code': product.code,
                'barcode': product.barcode,
                'price': float(product.selling_price),
                'quantity': product.quantity,
                'category': product.category.name
            })

        return JsonResponse({'products': data})

    return JsonResponse({'error': 'Method not allowed'}, status=405)
