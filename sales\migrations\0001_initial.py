# Generated by Django 4.2.7 on 2025-07-10 13:06

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('products', '0001_initial'),
        ('customers', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Sale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(max_length=50, unique=True, verbose_name='رقم الفاتورة')),
                ('payment_method', models.CharField(choices=[('cash', 'نقدي'), ('credit', 'آجل'), ('card', 'بطاقة'), ('mixed', 'مختلط')], default='cash', max_length=20, verbose_name='طريقة الدفع')),
                ('status', models.CharField(choices=[('pending', 'معلق'), ('completed', 'مكتمل'), ('cancelled', 'ملغي'), ('returned', 'مرتجع')], default='completed', max_length=20, verbose_name='الحالة')),
                ('subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='المجموع الفرعي')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='مبلغ الخصم')),
                ('discount_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0)], verbose_name='نسبة الخصم')),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='مبلغ الضريبة')),
                ('tax_percentage', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0)], verbose_name='نسبة الضريبة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='المبلغ الإجمالي')),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='المبلغ المدفوع')),
                ('change_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الباقي')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('sale_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ البيع')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('cashier', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='الكاشير')),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='sales', to='customers.customer', verbose_name='العميل')),
            ],
            options={
                'verbose_name': 'بيع',
                'verbose_name_plural': 'المبيعات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='SaleReturn',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('return_number', models.CharField(max_length=50, unique=True, verbose_name='رقم المرتجع')),
                ('return_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='مبلغ المرتجع')),
                ('reason', models.TextField(verbose_name='سبب المرتجع')),
                ('return_date', models.DateTimeField(default=django.utils.timezone.now, verbose_name='تاريخ المرتجع')),
                ('original_sale', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='returns', to='sales.sale', verbose_name='البيع الأصلي')),
                ('processed_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='معالج بواسطة')),
            ],
            options={
                'verbose_name': 'مرتجع بيع',
                'verbose_name_plural': 'مرتجعات المبيعات',
                'ordering': ['-return_date'],
            },
        ),
        migrations.CreateModel(
            name='SaleReturnItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='سعر الوحدة')),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر الإجمالي')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='products.product', verbose_name='المنتج')),
                ('sale_return', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='sales.salereturn', verbose_name='المرتجع')),
            ],
            options={
                'verbose_name': 'عنصر مرتجع بيع',
                'verbose_name_plural': 'عناصر مرتجعات البيع',
            },
        ),
        migrations.CreateModel(
            name='SaleItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='الكمية')),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='سعر الوحدة')),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='السعر الإجمالي')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='products.product', verbose_name='المنتج')),
                ('sale', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='sales.sale', verbose_name='البيع')),
            ],
            options={
                'verbose_name': 'عنصر بيع',
                'verbose_name_plural': 'عناصر البيع',
            },
        ),
    ]
