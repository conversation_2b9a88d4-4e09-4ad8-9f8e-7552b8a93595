from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone
from decimal import Decimal


class AccountType(models.Model):
    """أنواع الحسابات"""

    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name='اسم نوع الحساب'
    )
    code = models.CharField(
        max_length=10,
        unique=True,
        verbose_name='كود النوع'
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name='الوصف'
    )

    class Meta:
        verbose_name = 'نوع حساب'
        verbose_name_plural = 'أنواع الحسابات'
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"


class Account(models.Model):
    """دليل الحسابات"""

    ACCOUNT_NATURE = [
        ('debit', 'مدين'),
        ('credit', 'دائن'),
    ]

    code = models.Cha<PERSON><PERSON><PERSON>(
        max_length=20,
        unique=True,
        verbose_name='كود الحساب'
    )
    name = models.CharField(
        max_length=200,
        verbose_name='اسم الحساب'
    )
    account_type = models.ForeignKey(
        AccountType,
        on_delete=models.PROTECT,
        related_name='accounts',
        verbose_name='نوع الحساب'
    )
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='sub_accounts',
        verbose_name='الحساب الأب'
    )
    nature = models.CharField(
        max_length=10,
        choices=ACCOUNT_NATURE,
        verbose_name='طبيعة الحساب'
    )
    opening_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name='الرصيد الافتتاحي'
    )
    current_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name='الرصيد الحالي'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    class Meta:
        verbose_name = 'حساب'
        verbose_name_plural = 'دليل الحسابات'
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"


class JournalEntry(models.Model):
    """القيود اليومية"""

    entry_number = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='رقم القيد'
    )
    date = models.DateField(
        default=timezone.now,
        verbose_name='التاريخ'
    )
    description = models.TextField(
        verbose_name='البيان'
    )
    reference = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='المرجع'
    )
    total_debit = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name='إجمالي المدين'
    )
    total_credit = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name='إجمالي الدائن'
    )
    is_posted = models.BooleanField(
        default=False,
        verbose_name='مرحل'
    )
    created_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        verbose_name='أنشأ بواسطة'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    class Meta:
        verbose_name = 'قيد يومية'
        verbose_name_plural = 'القيود اليومية'
        ordering = ['-date', '-created_at']

    def __str__(self):
        return f"قيد {self.entry_number} - {self.date}"

    @property
    def is_balanced(self):
        """تحقق من توازن القيد"""
        return self.total_debit == self.total_credit

    def calculate_totals(self):
        """حساب مجاميع القيد"""
        self.total_debit = sum(
            detail.debit_amount for detail in self.details.all()
        )
        self.total_credit = sum(
            detail.credit_amount for detail in self.details.all()
        )


class JournalEntryDetail(models.Model):
    """تفاصيل القيد اليومي"""

    journal_entry = models.ForeignKey(
        JournalEntry,
        on_delete=models.CASCADE,
        related_name='details',
        verbose_name='القيد اليومي'
    )
    account = models.ForeignKey(
        Account,
        on_delete=models.PROTECT,
        verbose_name='الحساب'
    )
    debit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='مبلغ مدين'
    )
    credit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='مبلغ دائن'
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name='البيان'
    )

    class Meta:
        verbose_name = 'تفصيل قيد يومي'
        verbose_name_plural = 'تفاصيل القيود اليومية'

    def __str__(self):
        return f"{self.account.name} - {self.debit_amount or self.credit_amount}"


class CashBox(models.Model):
    """الخزينة"""

    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name='اسم الخزينة'
    )
    opening_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name='الرصيد الافتتاحي'
    )
    current_balance = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        verbose_name='الرصيد الحالي'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشطة'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    class Meta:
        verbose_name = 'خزينة'
        verbose_name_plural = 'الخزائن'
        ordering = ['name']

    def __str__(self):
        return self.name


class CashTransaction(models.Model):
    """معاملات الخزينة"""

    TRANSACTION_TYPES = [
        ('in', 'إيداع'),
        ('out', 'سحب'),
        ('transfer', 'تحويل'),
    ]

    cash_box = models.ForeignKey(
        CashBox,
        on_delete=models.CASCADE,
        related_name='transactions',
        verbose_name='الخزينة'
    )
    transaction_type = models.CharField(
        max_length=20,
        choices=TRANSACTION_TYPES,
        verbose_name='نوع المعاملة'
    )
    amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name='المبلغ'
    )
    description = models.TextField(
        verbose_name='البيان'
    )
    reference = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='المرجع'
    )
    created_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        verbose_name='أنشأ بواسطة'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ المعاملة'
    )

    class Meta:
        verbose_name = 'معاملة خزينة'
        verbose_name_plural = 'معاملات الخزينة'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.cash_box.name} - {self.get_transaction_type_display()} - {self.amount}"

    def save(self, *args, **kwargs):
        """تحديث رصيد الخزينة"""
        is_new = self.pk is None

        if is_new:
            if self.transaction_type == 'in':
                self.cash_box.current_balance += self.amount
            elif self.transaction_type == 'out':
                self.cash_box.current_balance -= self.amount

            self.cash_box.save()

        super().save(*args, **kwargs)
