# Generated by Django 4.2.7 on 2025-07-10 13:06

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(default='مكتبة سنتر آدم', max_length=200, verbose_name='اسم الشركة')),
                ('company_address', models.TextField(blank=True, null=True, verbose_name='عنوان الشركة')),
                ('company_phone', models.CharField(blank=True, max_length=15, null=True, verbose_name='هاتف الشركة')),
                ('company_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='بريد الشركة')),
                ('company_logo', models.ImageField(blank=True, null=True, upload_to='company/', verbose_name='شعار الشركة')),
                ('tax_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='الرقم الضريبي')),
                ('tax_enabled', models.BooleanField(default=False, verbose_name='تفعيل الضرائب')),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)], verbose_name='معدل الضريبة')),
                ('currency_code', models.CharField(default='EGP', max_length=3, verbose_name='رمز العملة')),
                ('currency_symbol', models.CharField(default='ج.م', max_length=5, verbose_name='رمز العملة')),
                ('invoice_prefix', models.CharField(default='INV', max_length=10, verbose_name='بادئة الفاتورة')),
                ('invoice_start_number', models.IntegerField(default=1, verbose_name='رقم بداية الفاتورة')),
                ('language', models.CharField(choices=[('ar', 'العربية'), ('en', 'English')], default='ar', max_length=5, verbose_name='اللغة')),
                ('theme', models.CharField(choices=[('light', 'فاتح'), ('dark', 'داكن')], default='light', max_length=10, verbose_name='المظهر')),
                ('backup_enabled', models.BooleanField(default=True, verbose_name='تفعيل النسخ الاحتياطي')),
                ('backup_frequency', models.CharField(choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري')], default='daily', max_length=10, verbose_name='تكرار النسخ الاحتياطي')),
                ('low_stock_alert', models.BooleanField(default=True, verbose_name='تنبيه انخفاض المخزون')),
                ('email_notifications', models.BooleanField(default=False, verbose_name='تنبيهات البريد الإلكتروني')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إعدادات النظام',
                'verbose_name_plural': 'إعدادات النظام',
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='العنوان')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('notification_type', models.CharField(choices=[('info', 'معلومات'), ('warning', 'تحذير'), ('error', 'خطأ'), ('success', 'نجاح')], default='info', max_length=10, verbose_name='نوع التنبيه')),
                ('is_read', models.BooleanField(default=False, verbose_name='مقروء')),
                ('is_global', models.BooleanField(default=False, verbose_name='عام لجميع المستخدمين')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'تنبيه',
                'verbose_name_plural': 'التنبيهات',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Branch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الفرع')),
                ('address', models.TextField(verbose_name='العنوان')),
                ('phone', models.CharField(blank=True, max_length=15, null=True, verbose_name='الهاتف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_branches', to=settings.AUTH_USER_MODEL, verbose_name='مدير الفرع')),
            ],
            options={
                'verbose_name': 'فرع',
                'verbose_name_plural': 'الفروع',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='BackupLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('filename', models.CharField(max_length=200, verbose_name='اسم الملف')),
                ('file_size', models.BigIntegerField(default=0, verbose_name='حجم الملف')),
                ('status', models.CharField(choices=[('success', 'نجح'), ('failed', 'فشل'), ('in_progress', 'قيد التنفيذ')], max_length=20, verbose_name='الحالة')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='رسالة الخطأ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
            ],
            options={
                'verbose_name': 'سجل نسخة احتياطية',
                'verbose_name_plural': 'سجل النسخ الاحتياطية',
                'ordering': ['-created_at'],
            },
        ),
    ]
