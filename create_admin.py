#!/usr/bin/env python
import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sentr_adam.settings')
django.setup()

from authentication.models import User

# إنشاء مستخدم مدير
try:
    # التحقق من وجود مستخدم مدير
    if User.objects.filter(username='admin').exists():
        print("✅ المستخدم 'admin' موجود بالفعل")
        admin_user = User.objects.get(username='admin')
    else:
        # إنشاء مستخدم مدير جديد
        admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            first_name='مدير',
            last_name='النظام',
            role='admin',
            is_staff=True,
            is_superuser=True,
            is_active=True,
            is_active_employee=True
        )
        print("✅ تم إنشاء المستخدم المدير بنجاح!")
    
    print(f"📋 بيانات المستخدم المدير:")
    print(f"   اسم المستخدم: {admin_user.username}")
    print(f"   البريد الإلكتروني: {admin_user.email}")
    print(f"   كلمة المرور: admin123")
    print(f"   الدور: {admin_user.get_role_display()}")
    print(f"   مدير: {admin_user.is_superuser}")
    print(f"   نشط: {admin_user.is_active}")
    
    # إنشاء بعض البيانات الأساسية
    from products.models import Category
    from settings_app.models import SystemSettings
    
    # إنشاء تصنيفات أساسية
    categories = [
        {'name': 'كتب', 'description': 'جميع أنواع الكتب'},
        {'name': 'أدوات مكتبية', 'description': 'أقلام، دفاتر، مساطر'},
        {'name': 'هدايا', 'description': 'هدايا متنوعة'},
        {'name': 'لعب أطفال', 'description': 'ألعاب تعليمية وترفيهية'},
    ]
    
    for cat_data in categories:
        category, created = Category.objects.get_or_create(
            name=cat_data['name'],
            defaults={'description': cat_data['description']}
        )
        if created:
            print(f"✅ تم إنشاء تصنيف: {category.name}")
    
    # إنشاء إعدادات النظام
    settings = SystemSettings.get_settings()
    print(f"✅ تم إنشاء إعدادات النظام: {settings.company_name}")
    
    print("\n🎉 تم إعداد النظام بنجاح!")
    print("🌐 يمكنك الآن تسجيل الدخول باستخدام:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    
except Exception as e:
    print(f"❌ خطأ: {str(e)}")
