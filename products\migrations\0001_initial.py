# Generated by Django 4.2.7 on 2025-07-10 13:06

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم التصنيف')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='products.category', verbose_name='التصنيف الأب')),
            ],
            options={
                'verbose_name': 'تصنيف',
                'verbose_name_plural': 'التصنيفات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم المنتج')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='كود المنتج')),
                ('barcode', models.CharField(blank=True, max_length=50, null=True, unique=True, verbose_name='الباركود')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('image', models.ImageField(blank=True, null=True, upload_to='products/', verbose_name='صورة المنتج')),
                ('cost_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='سعر التكلفة')),
                ('selling_price', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)], verbose_name='سعر البيع')),
                ('quantity', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الكمية المتاحة')),
                ('min_quantity', models.IntegerField(default=5, validators=[django.core.validators.MinValueValidator(0)], verbose_name='الحد الأدنى للكمية')),
                ('unit', models.CharField(default='قطعة', max_length=20, verbose_name='الوحدة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='products', to='products.category', verbose_name='التصنيف')),
            ],
            options={
                'verbose_name': 'منتج',
                'verbose_name_plural': 'المنتجات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='StockMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_type', models.CharField(choices=[('in', 'إدخال'), ('out', 'إخراج'), ('adjustment', 'تسوية'), ('return', 'مرتجع')], max_length=20, verbose_name='نوع الحركة')),
                ('quantity', models.IntegerField(verbose_name='الكمية')),
                ('reference', models.CharField(blank=True, max_length=100, null=True, verbose_name='المرجع')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الحركة')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_movements', to='products.product', verbose_name='المنتج')),
            ],
            options={
                'verbose_name': 'حركة مخزون',
                'verbose_name_plural': 'حركات المخزون',
                'ordering': ['-created_at'],
            },
        ),
    ]
