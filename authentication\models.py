from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone


class User(AbstractUser):
    """نموذج المستخدم المخصص"""

    ROLE_CHOICES = [
        ('admin', 'مدير'),
        ('cashier', 'كاشير'),
        ('accountant', 'محاسب'),
        ('warehouse_manager', 'مشرف مخازن'),
    ]

    role = models.CharField(
        max_length=20,
        choices=ROLE_CHOICES,
        default='cashier',
        verbose_name='الدور'
    )
    phone = models.CharField(
        max_length=15,
        blank=True,
        null=True,
        verbose_name='رقم الهاتف'
    )
    address = models.TextField(
        blank=True,
        null=True,
        verbose_name='العنوان'
    )
    is_active_employee = models.BooleanField(
        default=True,
        verbose_name='موظف نشط'
    )
    hire_date = models.DateField(
        default=timezone.now,
        verbose_name='تاريخ التوظيف'
    )
    salary = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        verbose_name='الراتب'
    )
    profile_image = models.ImageField(
        upload_to='profiles/',
        blank=True,
        null=True,
        verbose_name='صورة الملف الشخصي'
    )

    class Meta:
        verbose_name = 'مستخدم'
        verbose_name_plural = 'المستخدمون'

    def __str__(self):
        return f"{self.get_full_name()} ({self.get_role_display()})"

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}".strip() or self.username

    @property
    def can_manage_users(self):
        return self.role == 'admin'

    @property
    def can_access_accounting(self):
        return self.role in ['admin', 'accountant']

    @property
    def can_manage_inventory(self):
        return self.role in ['admin', 'warehouse_manager']

    @property
    def can_process_sales(self):
        return self.role in ['admin', 'cashier']


class UserActivity(models.Model):
    """سجل نشاطات المستخدمين"""

    ACTION_CHOICES = [
        ('login', 'تسجيل دخول'),
        ('logout', 'تسجيل خروج'),
        ('create', 'إنشاء'),
        ('update', 'تحديث'),
        ('delete', 'حذف'),
        ('view', 'عرض'),
        ('sale', 'بيع'),
        ('return', 'مرتجع'),
        ('payment', 'دفع'),
        ('report', 'تقرير'),
    ]

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='activities',
        verbose_name='المستخدم'
    )
    action = models.CharField(
        max_length=20,
        choices=ACTION_CHOICES,
        verbose_name='النشاط'
    )
    description = models.TextField(
        verbose_name='الوصف'
    )
    ip_address = models.GenericIPAddressField(
        blank=True,
        null=True,
        verbose_name='عنوان IP'
    )
    timestamp = models.DateTimeField(
        auto_now_add=True,
        verbose_name='وقت النشاط'
    )

    class Meta:
        verbose_name = 'نشاط مستخدم'
        verbose_name_plural = 'نشاطات المستخدمين'
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.get_action_display()} - {self.timestamp}"


class UserSession(models.Model):
    """جلسات المستخدمين"""

    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='sessions',
        verbose_name='المستخدم'
    )
    session_key = models.CharField(
        max_length=40,
        unique=True,
        verbose_name='مفتاح الجلسة'
    )
    ip_address = models.GenericIPAddressField(
        verbose_name='عنوان IP'
    )
    user_agent = models.TextField(
        blank=True,
        verbose_name='معلومات المتصفح'
    )
    login_time = models.DateTimeField(
        auto_now_add=True,
        verbose_name='وقت تسجيل الدخول'
    )
    last_activity = models.DateTimeField(
        auto_now=True,
        verbose_name='آخر نشاط'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='جلسة نشطة'
    )

    class Meta:
        verbose_name = 'جلسة مستخدم'
        verbose_name_plural = 'جلسات المستخدمين'
        ordering = ['-login_time']

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.login_time}"
