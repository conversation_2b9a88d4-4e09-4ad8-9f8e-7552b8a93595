from django.shortcuts import render
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import TemplateView, ListView, CreateView
from django.urls import reverse_lazy
from django.contrib import messages

from .models import Account, JournalEntry, CashBox, CashTransaction


class AccountingDashboardView(LoginRequiredMixin, TemplateView):
    """لوحة تحكم المحاسبة"""
    template_name = 'accounting/dashboard.html'


class AccountListView(LoginRequiredMixin, ListView):
    """قائمة الحسابات"""
    model = Account
    template_name = 'accounting/account_list.html'
    context_object_name = 'accounts'


class AccountCreateView(LoginRequiredMixin, CreateView):
    """إنشاء حساب جديد"""
    model = Account
    template_name = 'accounting/account_form.html'
    fields = ['code', 'name', 'account_type', 'parent', 'nature', 'opening_balance']
    success_url = reverse_lazy('accounting:account_list')


class JournalEntryListView(LoginRequiredMixin, ListView):
    """قائمة القيود اليومية"""
    model = JournalEntry
    template_name = 'accounting/journal_entry_list.html'
    context_object_name = 'entries'


class JournalEntryCreateView(LoginRequiredMixin, CreateView):
    """إنشاء قيد يومي جديد"""
    model = JournalEntry
    template_name = 'accounting/journal_entry_form.html'
    fields = ['date', 'description', 'reference']
    success_url = reverse_lazy('accounting:journal_entry_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        return super().form_valid(form)


class CashBoxListView(LoginRequiredMixin, ListView):
    """قائمة الخزائن"""
    model = CashBox
    template_name = 'accounting/cash_box_list.html'
    context_object_name = 'cash_boxes'


class CashTransactionListView(LoginRequiredMixin, ListView):
    """قائمة معاملات الخزينة"""
    model = CashTransaction
    template_name = 'accounting/cash_transaction_list.html'
    context_object_name = 'transactions'

    def get_queryset(self):
        cash_box_id = self.kwargs.get('pk')
        return CashTransaction.objects.filter(cash_box_id=cash_box_id)


class CashTransactionCreateView(LoginRequiredMixin, CreateView):
    """إنشاء معاملة خزينة جديدة"""
    model = CashTransaction
    template_name = 'accounting/cash_transaction_form.html'
    fields = ['transaction_type', 'amount', 'description', 'reference']

    def form_valid(self, form):
        cash_box_id = self.kwargs.get('pk')
        form.instance.cash_box_id = cash_box_id
        form.instance.created_by = self.request.user
        return super().form_valid(form)
