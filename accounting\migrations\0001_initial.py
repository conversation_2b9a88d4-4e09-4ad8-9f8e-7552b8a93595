# Generated by Django 4.2.7 on 2025-07-10 13:06

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=20, unique=True, verbose_name='كود الحساب')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الحساب')),
                ('nature', models.CharField(choices=[('debit', 'مدين'), ('credit', 'دائن')], max_length=10, verbose_name='طبيعة الحساب')),
                ('opening_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد الافتتاحي')),
                ('current_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد الحالي')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'حساب',
                'verbose_name_plural': 'دليل الحسابات',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='AccountType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم نوع الحساب')),
                ('code', models.CharField(max_length=10, unique=True, verbose_name='كود النوع')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
            ],
            options={
                'verbose_name': 'نوع حساب',
                'verbose_name_plural': 'أنواع الحسابات',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='CashBox',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم الخزينة')),
                ('opening_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد الافتتاحي')),
                ('current_balance', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='الرصيد الحالي')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشطة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'خزينة',
                'verbose_name_plural': 'الخزائن',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='CashTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('in', 'إيداع'), ('out', 'سحب'), ('transfer', 'تحويل')], max_length=20, verbose_name='نوع المعاملة')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='المبلغ')),
                ('description', models.TextField(verbose_name='البيان')),
                ('reference', models.CharField(blank=True, max_length=100, null=True, verbose_name='المرجع')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ المعاملة')),
            ],
            options={
                'verbose_name': 'معاملة خزينة',
                'verbose_name_plural': 'معاملات الخزينة',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='JournalEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('entry_number', models.CharField(max_length=50, unique=True, verbose_name='رقم القيد')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='التاريخ')),
                ('description', models.TextField(verbose_name='البيان')),
                ('reference', models.CharField(blank=True, max_length=100, null=True, verbose_name='المرجع')),
                ('total_debit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي المدين')),
                ('total_credit', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي الدائن')),
                ('is_posted', models.BooleanField(default=False, verbose_name='مرحل')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'قيد يومية',
                'verbose_name_plural': 'القيود اليومية',
                'ordering': ['-date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='JournalEntryDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('debit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='مبلغ مدين')),
                ('credit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, validators=[django.core.validators.MinValueValidator(0)], verbose_name='مبلغ دائن')),
                ('description', models.TextField(blank=True, null=True, verbose_name='البيان')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='accounting.account', verbose_name='الحساب')),
                ('journal_entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='details', to='accounting.journalentry', verbose_name='القيد اليومي')),
            ],
            options={
                'verbose_name': 'تفصيل قيد يومي',
                'verbose_name_plural': 'تفاصيل القيود اليومية',
            },
        ),
    ]
