# Generated by Django 4.2.7 on 2025-07-10 13:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounting', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='journalentry',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة'),
        ),
        migrations.AddField(
            model_name='cashtransaction',
            name='cash_box',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='accounting.cashbox', verbose_name='الخزينة'),
        ),
        migrations.AddField(
            model_name='cashtransaction',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة'),
        ),
        migrations.AddField(
            model_name='account',
            name='account_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='accounts', to='accounting.accounttype', verbose_name='نوع الحساب'),
        ),
        migrations.AddField(
            model_name='account',
            name='parent',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sub_accounts', to='accounting.account', verbose_name='الحساب الأب'),
        ),
    ]
