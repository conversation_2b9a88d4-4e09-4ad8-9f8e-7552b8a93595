"""
URL configuration for sentr_adam project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import HttpResponse

def home_view(request):
    return HttpResponse("""
    <html dir="rtl">
    <head>
        <title>مكتبة سنتر آدم</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial; text-align: center; padding: 50px; }
            h1 { color: #667eea; }
            .btn {
                display: inline-block;
                padding: 10px 20px;
                margin: 10px;
                background: #667eea;
                color: white;
                text-decoration: none;
                border-radius: 5px;
            }
        </style>
    </head>
    <body>
        <h1>🏪 مرحباً بك في مكتبة سنتر آدم</h1>
        <p>نظام إدارة متكامل للمكتبات</p>
        <a href="/admin/" class="btn">لوحة الإدارة</a>
        <a href="/auth/login/" class="btn">تسجيل الدخول</a>
    </body>
    </html>
    """)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', home_view, name='home'),
    path('auth/', include('authentication.urls')),
]

# إضافة ملفات الوسائط في وضع التطوير
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
