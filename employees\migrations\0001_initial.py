# Generated by Django 4.2.7 on 2025-07-10 13:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم القسم')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='managed_departments', to=settings.AUTH_USER_MODEL, verbose_name='مدير القسم')),
            ],
            options={
                'verbose_name': 'قسم',
                'verbose_name_plural': 'الأقسام',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Shift',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الوردية')),
                ('start_time', models.TimeField(verbose_name='وقت البداية')),
                ('end_time', models.TimeField(verbose_name='وقت النهاية')),
                ('break_duration', models.IntegerField(default=0, help_text='مدة الاستراحة بالدقائق', verbose_name='مدة الاستراحة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشطة')),
            ],
            options={
                'verbose_name': 'وردية',
                'verbose_name_plural': 'الورديات',
                'ordering': ['start_time'],
            },
        ),
        migrations.CreateModel(
            name='EmployeeProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('employee_id', models.CharField(max_length=20, unique=True, verbose_name='رقم الموظف')),
                ('position', models.CharField(max_length=100, verbose_name='المنصب')),
                ('national_id', models.CharField(max_length=20, unique=True, verbose_name='رقم الهوية')),
                ('emergency_contact', models.CharField(blank=True, max_length=15, null=True, verbose_name='جهة اتصال الطوارئ')),
                ('bank_account', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الحساب البنكي')),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='employees', to='employees.department', verbose_name='القسم')),
                ('shift', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='employees', to='employees.shift', verbose_name='الوردية')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='employee_profile', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'ملف موظف',
                'verbose_name_plural': 'ملفات الموظفين',
                'ordering': ['employee_id'],
            },
        ),
        migrations.CreateModel(
            name='Payroll',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('month', models.IntegerField(verbose_name='الشهر')),
                ('year', models.IntegerField(verbose_name='السنة')),
                ('basic_salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='الراتب الأساسي')),
                ('allowances', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='البدلات')),
                ('overtime_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='مبلغ الساعات الإضافية')),
                ('deductions', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='الخصومات')),
                ('net_salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='صافي الراتب')),
                ('is_paid', models.BooleanField(default=False, verbose_name='مدفوع')),
                ('payment_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الدفع')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='created_payrolls', to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payrolls', to=settings.AUTH_USER_MODEL, verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'كشف راتب',
                'verbose_name_plural': 'كشوف الرواتب',
                'ordering': ['-year', '-month'],
                'unique_together': {('employee', 'month', 'year')},
            },
        ),
        migrations.CreateModel(
            name='Attendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(default=django.utils.timezone.now, verbose_name='التاريخ')),
                ('check_in', models.TimeField(blank=True, null=True, verbose_name='وقت الحضور')),
                ('check_out', models.TimeField(blank=True, null=True, verbose_name='وقت الانصراف')),
                ('break_start', models.TimeField(blank=True, null=True, verbose_name='بداية الاستراحة')),
                ('break_end', models.TimeField(blank=True, null=True, verbose_name='نهاية الاستراحة')),
                ('total_hours', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='إجمالي الساعات')),
                ('overtime_hours', models.DecimalField(decimal_places=2, default=0, max_digits=5, verbose_name='ساعات إضافية')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_records', to=settings.AUTH_USER_MODEL, verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'حضور',
                'verbose_name_plural': 'سجل الحضور',
                'ordering': ['-date'],
                'unique_together': {('employee', 'date')},
            },
        ),
    ]
