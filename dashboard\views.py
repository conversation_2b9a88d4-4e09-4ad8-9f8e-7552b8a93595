from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import TemplateView, ListView
from django.http import JsonResponse
from django.db.models import Sum, Count, Q, F, F
from django.utils import timezone
from datetime import datetime, timedelta
from django.contrib import messages
from django.contrib.auth.decorators import login_required

from sales.models import Sale, SaleItem
from products.models import Product, StockMovement
from customers.models import Customer
from suppliers.models import Supplier
from accounting.models import CashBox, CashTransaction
from settings_app.models import Notification


class DashboardView(LoginRequiredMixin, TemplateView):
    """لوحة التحكم الرئيسية"""
    template_name = 'dashboard/dashboard.html'

    def get(self, request, *args, **kwargs):
        # صفحة بسيطة مؤقتاً
        from django.http import HttpResponse
        return HttpResponse(f"""
        <html dir="rtl">
        <head>
            <title>لوحة التحكم - مكتبة سنتر آدم</title>
            <meta charset="utf-8">
            <style>
                body {{
                    font-family: Arial;
                    text-align: center;
                    padding: 50px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    min-height: 100vh;
                }}
                .container {{
                    background: white;
                    color: #333;
                    padding: 40px;
                    border-radius: 15px;
                    max-width: 800px;
                    margin: 0 auto;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                }}
                h1 {{ color: #667eea; margin-bottom: 30px; }}
                .welcome {{ font-size: 1.2em; margin-bottom: 30px; }}
                .btn {{
                    display: inline-block;
                    padding: 12px 25px;
                    margin: 10px;
                    background: #667eea;
                    color: white;
                    text-decoration: none;
                    border-radius: 8px;
                    transition: all 0.3s ease;
                }}
                .btn:hover {{
                    background: #5a6fd8;
                    transform: translateY(-2px);
                }}
                .stats {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 20px;
                    margin: 30px 0;
                }}
                .stat-card {{
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 10px;
                    border-left: 4px solid #667eea;
                }}
                .logout {{
                    position: absolute;
                    top: 20px;
                    right: 20px;
                    background: #dc3545;
                }}
            </style>
        </head>
        <body>
            <a href="/auth/logout/" class="btn logout">تسجيل الخروج</a>

            <div class="container">
                <h1>🏪 مرحباً بك في لوحة التحكم</h1>
                <div class="welcome">
                    أهلاً وسهلاً <strong>{request.user.get_full_name()}</strong> في نظام إدارة مكتبة سنتر آدم
                </div>

                <div class="stats">
                    <div class="stat-card">
                        <h3>📊 المبيعات اليوم</h3>
                        <p>0.00 ج.م</p>
                    </div>
                    <div class="stat-card">
                        <h3>📦 إجمالي المنتجات</h3>
                        <p>0 منتج</p>
                    </div>
                    <div class="stat-card">
                        <h3>👥 العملاء</h3>
                        <p>0 عميل</p>
                    </div>
                    <div class="stat-card">
                        <h3>💰 رصيد الخزينة</h3>
                        <p>0.00 ج.م</p>
                    </div>
                </div>

                <div>
                    <a href="/admin/" class="btn">🔧 لوحة الإدارة</a>
                    <a href="#" class="btn">💰 نقطة البيع</a>
                    <a href="#" class="btn">📦 المنتجات</a>
                    <a href="#" class="btn">👥 العملاء</a>
                    <a href="#" class="btn">📊 التقارير</a>
                </div>

                <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee; color: #666;">
                    <p>نظام إدارة متكامل للمكتبات - الإصدار 1.0</p>
                    <p>تم تسجيل الدخول بنجاح كـ: <strong>{request.user.get_role_display()}</strong></p>
                </div>
            </div>
        </body>
        </html>
        """)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # تواريخ للإحصائيات
        today = timezone.now().date()
        yesterday = today - timedelta(days=1)
        this_month_start = today.replace(day=1)
        last_month_start = (this_month_start - timedelta(days=1)).replace(day=1)

        # إحصائيات المبيعات
        today_sales = Sale.objects.filter(
            sale_date__date=today,
            status='completed'
        ).aggregate(
            total=Sum('total_amount'),
            count=Count('id')
        )

        yesterday_sales = Sale.objects.filter(
            sale_date__date=yesterday,
            status='completed'
        ).aggregate(
            total=Sum('total_amount'),
            count=Count('id')
        )

        this_month_sales = Sale.objects.filter(
            sale_date__date__gte=this_month_start,
            status='completed'
        ).aggregate(
            total=Sum('total_amount'),
            count=Count('id')
        )

        # إحصائيات المنتجات
        total_products = Product.objects.filter(is_active=True).count()
        low_stock_products = Product.objects.filter(
            is_active=True,
            quantity__lte=F('min_quantity')
        ).count()

        # إحصائيات العملاء والموردين
        total_customers = Customer.objects.filter(is_active=True).count()
        total_suppliers = Supplier.objects.filter(is_active=True).count()

        # رصيد الخزينة
        cash_balance = CashBox.objects.filter(is_active=True).aggregate(
            total=Sum('current_balance')
        )['total'] or 0

        # أحدث المبيعات
        recent_sales = Sale.objects.filter(
            status='completed'
        ).select_related('customer', 'cashier').order_by('-created_at')[:5]

        # المنتجات الأكثر مبيعاً
        top_products = SaleItem.objects.filter(
            sale__sale_date__date__gte=this_month_start,
            sale__status='completed'
        ).values(
            'product__name'
        ).annotate(
            total_quantity=Sum('quantity'),
            total_revenue=Sum('total_price')
        ).order_by('-total_quantity')[:5]

        # التنبيهات
        notifications = Notification.objects.filter(
            Q(user=self.request.user) | Q(is_global=True),
            is_read=False
        ).order_by('-created_at')[:5]

        # حساب النسب المئوية للتغيير
        sales_change = 0
        if yesterday_sales['total'] and today_sales['total']:
            sales_change = ((today_sales['total'] - yesterday_sales['total']) / yesterday_sales['total']) * 100

        context.update({
            'today_sales': today_sales,
            'yesterday_sales': yesterday_sales,
            'this_month_sales': this_month_sales,
            'sales_change': round(sales_change, 2),
            'total_products': total_products,
            'low_stock_products': low_stock_products,
            'total_customers': total_customers,
            'total_suppliers': total_suppliers,
            'cash_balance': cash_balance,
            'recent_sales': recent_sales,
            'top_products': top_products,
            'notifications': notifications,
        })

        return context


class StatsView(LoginRequiredMixin, TemplateView):
    """صفحة الإحصائيات المفصلة"""
    template_name = 'dashboard/stats.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # إحصائيات مفصلة للمبيعات (آخر 30 يوم)
        thirty_days_ago = timezone.now().date() - timedelta(days=30)

        daily_sales = Sale.objects.filter(
            sale_date__date__gte=thirty_days_ago,
            status='completed'
        ).extra(
            select={'day': 'date(sale_date)'}
        ).values('day').annotate(
            total=Sum('total_amount'),
            count=Count('id')
        ).order_by('day')

        # إحصائيات المنتجات حسب التصنيف
        category_stats = Product.objects.filter(
            is_active=True
        ).values(
            'category__name'
        ).annotate(
            count=Count('id'),
            total_value=Sum(models.F('quantity') * models.F('cost_price'))
        ).order_by('-count')

        context.update({
            'daily_sales': list(daily_sales),
            'category_stats': list(category_stats),
        })

        return context


class NotificationListView(LoginRequiredMixin, ListView):
    """قائمة التنبيهات"""
    model = Notification
    template_name = 'dashboard/notifications.html'
    context_object_name = 'notifications'
    paginate_by = 20

    def get_queryset(self):
        return Notification.objects.filter(
            Q(user=self.request.user) | Q(is_global=True)
        ).order_by('-created_at')


@login_required
def mark_notification_read(request, pk):
    """تحديد التنبيه كمقروء"""
    notification = get_object_or_404(Notification, pk=pk)

    if notification.user == request.user or notification.is_global:
        notification.is_read = True
        notification.save()
        messages.success(request, 'تم تحديد التنبيه كمقروء')

    return redirect('dashboard:notifications')
