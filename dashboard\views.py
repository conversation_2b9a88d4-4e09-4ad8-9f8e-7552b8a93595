from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import TemplateView, ListView
from django.http import JsonResponse
from django.db.models import Sum, Count, Q
from django.utils import timezone
from datetime import datetime, timedelta
from django.contrib import messages
from django.contrib.auth.decorators import login_required

from sales.models import Sale, SaleItem
from products.models import Product, StockMovement
from customers.models import Customer
from suppliers.models import Supplier
from accounting.models import CashBox, CashTransaction
from settings_app.models import Notification


class DashboardView(LoginRequiredMixin, TemplateView):
    """لوحة التحكم الرئيسية"""
    template_name = 'dashboard/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # تواريخ للإحصائيات
        today = timezone.now().date()
        yesterday = today - timedelta(days=1)
        this_month_start = today.replace(day=1)
        last_month_start = (this_month_start - timedelta(days=1)).replace(day=1)

        # إحصائيات المبيعات
        today_sales = Sale.objects.filter(
            sale_date__date=today,
            status='completed'
        ).aggregate(
            total=Sum('total_amount'),
            count=Count('id')
        )

        yesterday_sales = Sale.objects.filter(
            sale_date__date=yesterday,
            status='completed'
        ).aggregate(
            total=Sum('total_amount'),
            count=Count('id')
        )

        this_month_sales = Sale.objects.filter(
            sale_date__date__gte=this_month_start,
            status='completed'
        ).aggregate(
            total=Sum('total_amount'),
            count=Count('id')
        )

        # إحصائيات المنتجات
        total_products = Product.objects.filter(is_active=True).count()
        low_stock_products = Product.objects.filter(
            is_active=True,
            quantity__lte=F('min_quantity')
        ).count()

        # إحصائيات العملاء والموردين
        total_customers = Customer.objects.filter(is_active=True).count()
        total_suppliers = Supplier.objects.filter(is_active=True).count()

        # رصيد الخزينة
        cash_balance = CashBox.objects.filter(is_active=True).aggregate(
            total=Sum('current_balance')
        )['total'] or 0

        # أحدث المبيعات
        recent_sales = Sale.objects.filter(
            status='completed'
        ).select_related('customer', 'cashier').order_by('-created_at')[:5]

        # المنتجات الأكثر مبيعاً
        top_products = SaleItem.objects.filter(
            sale__sale_date__date__gte=this_month_start,
            sale__status='completed'
        ).values(
            'product__name'
        ).annotate(
            total_quantity=Sum('quantity'),
            total_revenue=Sum('total_price')
        ).order_by('-total_quantity')[:5]

        # التنبيهات
        notifications = Notification.objects.filter(
            Q(user=self.request.user) | Q(is_global=True),
            is_read=False
        ).order_by('-created_at')[:5]

        # حساب النسب المئوية للتغيير
        sales_change = 0
        if yesterday_sales['total'] and today_sales['total']:
            sales_change = ((today_sales['total'] - yesterday_sales['total']) / yesterday_sales['total']) * 100

        context.update({
            'today_sales': today_sales,
            'yesterday_sales': yesterday_sales,
            'this_month_sales': this_month_sales,
            'sales_change': round(sales_change, 2),
            'total_products': total_products,
            'low_stock_products': low_stock_products,
            'total_customers': total_customers,
            'total_suppliers': total_suppliers,
            'cash_balance': cash_balance,
            'recent_sales': recent_sales,
            'top_products': top_products,
            'notifications': notifications,
        })

        return context


class StatsView(LoginRequiredMixin, TemplateView):
    """صفحة الإحصائيات المفصلة"""
    template_name = 'dashboard/stats.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # إحصائيات مفصلة للمبيعات (آخر 30 يوم)
        thirty_days_ago = timezone.now().date() - timedelta(days=30)

        daily_sales = Sale.objects.filter(
            sale_date__date__gte=thirty_days_ago,
            status='completed'
        ).extra(
            select={'day': 'date(sale_date)'}
        ).values('day').annotate(
            total=Sum('total_amount'),
            count=Count('id')
        ).order_by('day')

        # إحصائيات المنتجات حسب التصنيف
        category_stats = Product.objects.filter(
            is_active=True
        ).values(
            'category__name'
        ).annotate(
            count=Count('id'),
            total_value=Sum(models.F('quantity') * models.F('cost_price'))
        ).order_by('-count')

        context.update({
            'daily_sales': list(daily_sales),
            'category_stats': list(category_stats),
        })

        return context


class NotificationListView(LoginRequiredMixin, ListView):
    """قائمة التنبيهات"""
    model = Notification
    template_name = 'dashboard/notifications.html'
    context_object_name = 'notifications'
    paginate_by = 20

    def get_queryset(self):
        return Notification.objects.filter(
            Q(user=self.request.user) | Q(is_global=True)
        ).order_by('-created_at')


@login_required
def mark_notification_read(request, pk):
    """تحديد التنبيه كمقروء"""
    notification = get_object_or_404(Notification, pk=pk)

    if notification.user == request.user or notification.is_global:
        notification.is_read = True
        notification.save()
        messages.success(request, 'تم تحديد التنبيه كمقروء')

    return redirect('dashboard:notifications')
