from django.shortcuts import render
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import ListView, DetailView, CreateView
from django.urls import reverse_lazy

from authentication.models import User
from .models import EmployeeProfile, Attendance, Payroll, Department, Shift


class EmployeeListView(LoginRequiredMixin, ListView):
    """قائمة الموظفين"""
    model = User
    template_name = 'employees/employee_list.html'
    context_object_name = 'employees'

    def get_queryset(self):
        return User.objects.filter(is_active_employee=True)


class EmployeeDetailView(LoginRequiredMixin, DetailView):
    """تفاصيل الموظف"""
    model = User
    template_name = 'employees/employee_detail.html'
    context_object_name = 'employee'


class AttendanceListView(LoginRequiredMixin, ListView):
    """قائمة الحضور"""
    model = Attendance
    template_name = 'employees/attendance_list.html'
    context_object_name = 'attendance_records'


class AttendanceCreateView(LoginRequiredMixin, CreateView):
    """تسجيل حضور"""
    model = Attendance
    template_name = 'employees/attendance_form.html'
    fields = ['employee', 'date', 'check_in', 'check_out', 'notes']
    success_url = reverse_lazy('employees:attendance_list')


class PayrollListView(LoginRequiredMixin, ListView):
    """قائمة كشوف الرواتب"""
    model = Payroll
    template_name = 'employees/payroll_list.html'
    context_object_name = 'payrolls'


class PayrollCreateView(LoginRequiredMixin, CreateView):
    """إنشاء كشف راتب"""
    model = Payroll
    template_name = 'employees/payroll_form.html'
    fields = ['employee', 'month', 'year', 'basic_salary', 'allowances', 'deductions']
    success_url = reverse_lazy('employees:payroll_list')

    def form_valid(self, form):
        form.instance.created_by = self.request.user
        form.instance.calculate_net_salary()
        return super().form_valid(form)


class DepartmentListView(LoginRequiredMixin, ListView):
    """قائمة الأقسام"""
    model = Department
    template_name = 'employees/department_list.html'
    context_object_name = 'departments'


class ShiftListView(LoginRequiredMixin, ListView):
    """قائمة الورديات"""
    model = Shift
    template_name = 'employees/shift_list.html'
    context_object_name = 'shifts'
