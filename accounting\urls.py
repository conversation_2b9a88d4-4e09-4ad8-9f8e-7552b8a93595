from django.urls import path
from . import views

app_name = 'accounting'

urlpatterns = [
    path('', views.AccountingDashboardView.as_view(), name='dashboard'),
    path('accounts/', views.AccountListView.as_view(), name='account_list'),
    path('accounts/create/', views.AccountCreateView.as_view(), name='account_create'),
    path('journal-entries/', views.JournalEntryListView.as_view(), name='journal_entry_list'),
    path('journal-entries/create/', views.JournalEntryCreateView.as_view(), name='journal_entry_create'),
    path('cash-boxes/', views.CashBoxListView.as_view(), name='cash_box_list'),
    path('cash-boxes/<int:pk>/transactions/', views.CashTransactionListView.as_view(), name='cash_transactions'),
    path('cash-boxes/<int:pk>/transactions/create/', views.CashTransactionCreateView.as_view(), name='cash_transaction_create'),
]
