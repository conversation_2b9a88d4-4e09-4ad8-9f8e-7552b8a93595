# Generated by Django 4.2.7 on 2025-07-10 13:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ReportTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم التقرير')),
                ('report_type', models.CharField(choices=[('sales', 'تقارير المبيعات'), ('inventory', 'تقارير المخزون'), ('financial', 'التقارير المالية'), ('customer', 'تقارير العملاء'), ('supplier', 'تقارير الموردين'), ('employee', 'تقارير الموظفين')], max_length=20, verbose_name='نوع التقرير')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('sql_query', models.TextField(verbose_name='استعلام SQL')),
                ('parameters', models.JSONField(blank=True, default=dict, verbose_name='المعاملات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
            ],
            options={
                'verbose_name': 'قالب تقرير',
                'verbose_name_plural': 'قوالب التقارير',
                'ordering': ['report_type', 'name'],
            },
        ),
        migrations.CreateModel(
            name='ReportSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم الجدولة')),
                ('frequency', models.CharField(choices=[('daily', 'يومي'), ('weekly', 'أسبوعي'), ('monthly', 'شهري'), ('quarterly', 'ربع سنوي'), ('yearly', 'سنوي')], max_length=20, verbose_name='التكرار')),
                ('parameters', models.JSONField(default=dict, verbose_name='المعاملات')),
                ('recipients', models.JSONField(default=list, verbose_name='المستلمين')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('last_run', models.DateTimeField(blank=True, null=True, verbose_name='آخر تشغيل')),
                ('next_run', models.DateTimeField(verbose_name='التشغيل التالي')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='أنشأ بواسطة')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='schedules', to='reports.reporttemplate', verbose_name='قالب التقرير')),
            ],
            options={
                'verbose_name': 'جدولة تقرير',
                'verbose_name_plural': 'جدولة التقارير',
                'ordering': ['next_run'],
            },
        ),
        migrations.CreateModel(
            name='GeneratedReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='اسم التقرير')),
                ('format', models.CharField(choices=[('pdf', 'PDF'), ('excel', 'Excel'), ('csv', 'CSV')], max_length=10, verbose_name='التنسيق')),
                ('parameters', models.JSONField(default=dict, verbose_name='المعاملات المستخدمة')),
                ('file_path', models.CharField(blank=True, max_length=500, null=True, verbose_name='مسار الملف')),
                ('file_size', models.BigIntegerField(default=0, verbose_name='حجم الملف')),
                ('status', models.CharField(choices=[('generating', 'قيد التوليد'), ('completed', 'مكتمل'), ('failed', 'فشل')], default='generating', max_length=20, verbose_name='الحالة')),
                ('error_message', models.TextField(blank=True, null=True, verbose_name='رسالة الخطأ')),
                ('generated_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التوليد')),
                ('generated_by', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to=settings.AUTH_USER_MODEL, verbose_name='ولد بواسطة')),
                ('template', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='generated_reports', to='reports.reporttemplate', verbose_name='قالب التقرير')),
            ],
            options={
                'verbose_name': 'تقرير مُولد',
                'verbose_name_plural': 'التقارير المُولدة',
                'ordering': ['-generated_at'],
            },
        ),
    ]
