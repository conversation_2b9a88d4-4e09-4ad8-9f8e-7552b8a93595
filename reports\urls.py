from django.urls import path
from . import views

app_name = 'reports'

urlpatterns = [
    path('', views.ReportDashboardView.as_view(), name='dashboard'),
    path('sales/', views.SalesReportView.as_view(), name='sales_report'),
    path('inventory/', views.InventoryReportView.as_view(), name='inventory_report'),
    path('financial/', views.FinancialReportView.as_view(), name='financial_report'),
    path('customers/', views.CustomerReportView.as_view(), name='customer_report'),
    path('templates/', views.ReportTemplateListView.as_view(), name='template_list'),
    path('generated/', views.GeneratedReportListView.as_view(), name='generated_list'),
    path('generate/<int:template_id>/', views.generate_report, name='generate_report'),
]
