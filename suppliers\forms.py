from django import forms
from django.forms import inlineformset_factory
from .models import Supplier, PurchaseOrder, PurchaseOrderItem, SupplierTransaction
from products.models import Product


class SupplierForm(forms.ModelForm):
    """نموذج المورد"""
    
    class Meta:
        model = Supplier
        fields = ['name', 'company', 'phone', 'email', 'address', 'tax_number']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'company': forms.TextInput(attrs={'class': 'form-control'}),
            'phone': forms.TextInput(attrs={
                'class': 'form-control', 
                'dir': 'ltr',
                'placeholder': '+20xxxxxxxxx'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control', 
                'dir': 'ltr'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control', 
                'rows': 3
            }),
            'tax_number': forms.TextInput(attrs={
                'class': 'form-control', 
                'dir': 'ltr'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # إضافة تلميحات
        self.fields['phone'].help_text = 'رقم الهاتف مع كود الدولة'
        self.fields['tax_number'].help_text = 'الرقم الضريبي للمورد'
        
        # جعل بعض الحقول مطلوبة
        self.fields['name'].required = True
    
    def clean_phone(self):
        """التحقق من صحة رقم الهاتف"""
        phone = self.cleaned_data.get('phone')
        
        if phone:
            # إزالة المسافات والرموز
            phone = phone.replace(' ', '').replace('-', '').replace('(', '').replace(')', '')
            
            # التحقق من أن الرقم يحتوي على أرقام فقط
            if not phone.isdigit():
                raise forms.ValidationError('رقم الهاتف يجب أن يحتوي على أرقام فقط')
            
            # التحقق من طول الرقم
            if len(phone) < 10 or len(phone) > 15:
                raise forms.ValidationError('رقم الهاتف غير صحيح')
        
        return phone
    
    def clean_email(self):
        """التحقق من فرادة البريد الإلكتروني"""
        email = self.cleaned_data.get('email')
        
        if email:
            # التحقق من عدم وجود مورد آخر بنفس البريد
            existing = Supplier.objects.filter(email=email, is_active=True)
            if self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)
            
            if existing.exists():
                raise forms.ValidationError('يوجد مورد آخر بنفس البريد الإلكتروني')
        
        return email


class PurchaseOrderForm(forms.ModelForm):
    """نموذج أمر الشراء"""
    
    class Meta:
        model = PurchaseOrder
        fields = ['supplier', 'expected_delivery', 'notes']
        widgets = {
            'supplier': forms.Select(attrs={'class': 'form-select'}),
            'expected_delivery': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'form-control', 
                'rows': 3
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['supplier'].queryset = Supplier.objects.filter(is_active=True)
        
        # إضافة تلميحات
        self.fields['expected_delivery'].help_text = 'تاريخ التسليم المتوقع'
        
        # جعل بعض الحقول مطلوبة
        self.fields['supplier'].required = True


class PurchaseOrderItemForm(forms.ModelForm):
    """نموذج عنصر أمر الشراء"""
    
    class Meta:
        model = PurchaseOrderItem
        fields = ['product', 'quantity', 'unit_price']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-select product-select'}),
            'quantity': forms.NumberInput(attrs={
                'class': 'form-control quantity-input', 
                'min': '1'
            }),
            'unit_price': forms.NumberInput(attrs={
                'class': 'form-control price-input', 
                'step': '0.01',
                'min': '0'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['product'].queryset = Product.objects.filter(is_active=True)


# إنشاء FormSet لعناصر أمر الشراء
PurchaseOrderItemFormSet = inlineformset_factory(
    PurchaseOrder, 
    PurchaseOrderItem,
    form=PurchaseOrderItemForm,
    extra=1,
    min_num=1,
    validate_min=True,
    can_delete=True
)


class SupplierTransactionForm(forms.ModelForm):
    """نموذج معاملة المورد"""
    
    class Meta:
        model = SupplierTransaction
        fields = ['transaction_type', 'amount', 'reference', 'description']
        widgets = {
            'transaction_type': forms.Select(attrs={'class': 'form-select'}),
            'amount': forms.NumberInput(attrs={
                'class': 'form-control', 
                'step': '0.01',
                'min': '0.01'
            }),
            'reference': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={
                'class': 'form-control', 
                'rows': 3
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # إضافة تلميحات
        self.fields['reference'].help_text = 'رقم الفاتورة أو المرجع'
        self.fields['amount'].help_text = 'المبلغ بالجنيه المصري'
        
        # جعل بعض الحقول مطلوبة
        self.fields['transaction_type'].required = True
        self.fields['amount'].required = True
        self.fields['description'].required = True
    
    def clean_amount(self):
        """التحقق من صحة المبلغ"""
        amount = self.cleaned_data.get('amount')
        
        if amount and amount <= 0:
            raise forms.ValidationError('المبلغ يجب أن يكون أكبر من صفر')
        
        return amount


class SupplierSearchForm(forms.Form):
    """نموذج البحث عن الموردين"""
    
    search = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالاسم أو الشركة أو الهاتف...'
        })
    )
    
    has_balance = forms.ChoiceField(
        choices=[
            ('', 'الكل'),
            ('yes', 'لديه رصيد'),
            ('no', 'ليس لديه رصيد'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    order_by = forms.ChoiceField(
        choices=[
            ('name', 'الاسم'),
            ('company', 'الشركة'),
            ('current_balance', 'الرصيد'),
            ('created_at', 'تاريخ الإنشاء'),
        ],
        initial='name',
        widget=forms.Select(attrs={'class': 'form-select'})
    )


class PurchaseOrderSearchForm(forms.Form):
    """نموذج البحث في أوامر الشراء"""
    
    supplier = forms.ModelChoiceField(
        queryset=Supplier.objects.filter(is_active=True),
        required=False,
        empty_label="جميع الموردين",
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    status = forms.ChoiceField(
        choices=[('', 'جميع الحالات')] + PurchaseOrder.STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )
    
    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        })
    )


class SupplierBalanceAdjustmentForm(forms.Form):
    """نموذج تسوية رصيد المورد"""
    
    adjustment_type = forms.ChoiceField(
        choices=[
            ('increase', 'زيادة الرصيد'),
            ('decrease', 'تقليل الرصيد'),
            ('set', 'تحديد الرصيد'),
        ],
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    amount = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        min_value=0.01,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'step': '0.01'
        })
    )
    
    reason = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'سبب التسوية...'
        })
    )
    
    def __init__(self, supplier=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.supplier = supplier
        
        if supplier:
            self.fields['amount'].help_text = f'الرصيد الحالي: {supplier.current_balance} ج.م'
