from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone
import barcode
from barcode.writer import ImageWriter
from io import BytesIO
from django.core.files import File


class Category(models.Model):
    """تصنيفات المنتجات"""

    name = models.CharField(
        max_length=100,
        unique=True,
        verbose_name='اسم التصنيف'
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name='الوصف'
    )
    parent = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        blank=True,
        null=True,
        related_name='subcategories',
        verbose_name='التصنيف الأب'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )

    class Meta:
        verbose_name = 'تصنيف'
        verbose_name_plural = 'التصنيفات'
        ordering = ['name']

    def __str__(self):
        if self.parent:
            return f"{self.parent.name} - {self.name}"
        return self.name


class Product(models.Model):
    """المنتجات"""

    name = models.CharField(
        max_length=200,
        verbose_name='اسم المنتج'
    )
    code = models.CharField(
        max_length=50,
        unique=True,
        verbose_name='كود المنتج'
    )
    barcode = models.CharField(
        max_length=50,
        unique=True,
        blank=True,
        null=True,
        verbose_name='الباركود'
    )
    category = models.ForeignKey(
        Category,
        on_delete=models.PROTECT,
        related_name='products',
        verbose_name='التصنيف'
    )
    description = models.TextField(
        blank=True,
        null=True,
        verbose_name='الوصف'
    )
    image = models.ImageField(
        upload_to='products/',
        blank=True,
        null=True,
        verbose_name='صورة المنتج'
    )
    cost_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name='سعر التكلفة'
    )
    selling_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(0)],
        verbose_name='سعر البيع'
    )
    quantity = models.IntegerField(
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name='الكمية المتاحة'
    )
    min_quantity = models.IntegerField(
        default=5,
        validators=[MinValueValidator(0)],
        verbose_name='الحد الأدنى للكمية'
    )
    unit = models.CharField(
        max_length=20,
        default='قطعة',
        verbose_name='الوحدة'
    )
    is_active = models.BooleanField(
        default=True,
        verbose_name='نشط'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )

    class Meta:
        verbose_name = 'منتج'
        verbose_name_plural = 'المنتجات'
        ordering = ['name']

    def __str__(self):
        return f"{self.name} ({self.code})"

    @property
    def profit_margin(self):
        """هامش الربح"""
        if self.cost_price > 0:
            return ((self.selling_price - self.cost_price) / self.cost_price) * 100
        return 0

    @property
    def is_low_stock(self):
        """تحقق من انخفاض المخزون"""
        return self.quantity <= self.min_quantity

    @property
    def total_value(self):
        """القيمة الإجمالية للمخزون"""
        return self.quantity * self.cost_price

    def generate_barcode(self):
        """توليد باركود للمنتج"""
        if not self.barcode:
            # استخدام كود المنتج كباركود
            self.barcode = self.code

        # توليد صورة الباركود
        code128 = barcode.get_barcode_class('code128')
        barcode_instance = code128(self.barcode, writer=ImageWriter())

        buffer = BytesIO()
        barcode_instance.write(buffer)

        return buffer.getvalue()


class StockMovement(models.Model):
    """حركة المخزون"""

    MOVEMENT_TYPES = [
        ('in', 'إدخال'),
        ('out', 'إخراج'),
        ('adjustment', 'تسوية'),
        ('return', 'مرتجع'),
    ]

    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='stock_movements',
        verbose_name='المنتج'
    )
    movement_type = models.CharField(
        max_length=20,
        choices=MOVEMENT_TYPES,
        verbose_name='نوع الحركة'
    )
    quantity = models.IntegerField(
        verbose_name='الكمية'
    )
    reference = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        verbose_name='المرجع'
    )
    notes = models.TextField(
        blank=True,
        null=True,
        verbose_name='ملاحظات'
    )
    created_by = models.ForeignKey(
        'authentication.User',
        on_delete=models.PROTECT,
        verbose_name='أنشأ بواسطة'
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الحركة'
    )

    class Meta:
        verbose_name = 'حركة مخزون'
        verbose_name_plural = 'حركات المخزون'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.product.name} - {self.get_movement_type_display()} - {self.quantity}"

    def save(self, *args, **kwargs):
        """تحديث كمية المنتج عند حفظ الحركة"""
        is_new = self.pk is None

        if is_new:
            # تحديث كمية المنتج
            if self.movement_type in ['in', 'return']:
                self.product.quantity += self.quantity
            elif self.movement_type in ['out', 'adjustment']:
                self.product.quantity -= self.quantity

            self.product.save()

        super().save(*args, **kwargs)
