from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.http import JsonResponse
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.forms import PasswordChangeForm
from .models import User, UserActivity, UserSession
from .forms import UserCreationForm, UserUpdateForm, LoginForm


class LoginView(TemplateView):
    """صفحة تسجيل الدخول"""
    template_name = 'authentication/login.html'

    def get(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            return redirect('dashboard:home')
        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        form = LoginForm(request.POST)
        if form.is_valid():
            username = form.cleaned_data['username']
            password = form.cleaned_data['password']
            user = authenticate(request, username=username, password=password)

            if user is not None:
                if user.is_active:
                    login(request, user)

                    # تسجيل نشاط تسجيل الدخول
                    UserActivity.objects.create(
                        user=user,
                        action='login',
                        description=f'تسجيل دخول من {request.META.get("REMOTE_ADDR")}',
                        ip_address=request.META.get('REMOTE_ADDR')
                    )

                    # إنشاء جلسة مستخدم
                    UserSession.objects.create(
                        user=user,
                        session_key=request.session.session_key,
                        ip_address=request.META.get('REMOTE_ADDR'),
                        user_agent=request.META.get('HTTP_USER_AGENT', '')
                    )

                    messages.success(request, f'مرحباً {user.get_full_name()}')
                    next_url = request.GET.get('next', 'dashboard:home')
                    return redirect(next_url)
                else:
                    messages.error(request, 'حسابك غير نشط')
            else:
                messages.error(request, 'اسم المستخدم أو كلمة المرور غير صحيحة')

        return render(request, self.template_name, {'form': form})


class LogoutView(LoginRequiredMixin, TemplateView):
    """تسجيل الخروج"""

    def get(self, request, *args, **kwargs):
        # تسجيل نشاط تسجيل الخروج
        UserActivity.objects.create(
            user=request.user,
            action='logout',
            description=f'تسجيل خروج من {request.META.get("REMOTE_ADDR")}',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        # إنهاء الجلسة
        UserSession.objects.filter(
            user=request.user,
            session_key=request.session.session_key
        ).update(is_active=False)

        logout(request)
        messages.success(request, 'تم تسجيل الخروج بنجاح')
        return redirect('authentication:login')


class ProfileView(LoginRequiredMixin, TemplateView):
    """الملف الشخصي"""
    template_name = 'authentication/profile.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['user'] = self.request.user
        context['recent_activities'] = UserActivity.objects.filter(
            user=self.request.user
        ).order_by('-timestamp')[:10]
        return context


class ChangePasswordView(LoginRequiredMixin, TemplateView):
    """تغيير كلمة المرور"""
    template_name = 'authentication/change_password.html'

    def post(self, request, *args, **kwargs):
        form = PasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            messages.success(request, 'تم تغيير كلمة المرور بنجاح')

            # تسجيل النشاط
            UserActivity.objects.create(
                user=user,
                action='update',
                description='تغيير كلمة المرور',
                ip_address=request.META.get('REMOTE_ADDR')
            )

            return redirect('authentication:profile')

        return render(request, self.template_name, {'form': form})


class AdminRequiredMixin(UserPassesTestMixin):
    """التحقق من صلاحيات المدير"""

    def test_func(self):
        return self.request.user.is_authenticated and self.request.user.can_manage_users


class UserListView(AdminRequiredMixin, ListView):
    """قائمة المستخدمين"""
    model = User
    template_name = 'authentication/user_list.html'
    context_object_name = 'users'
    paginate_by = 20

    def get_queryset(self):
        return User.objects.filter(is_active=True).order_by('username')


class UserCreateView(AdminRequiredMixin, CreateView):
    """إنشاء مستخدم جديد"""
    model = User
    form_class = UserCreationForm
    template_name = 'authentication/user_form.html'
    success_url = reverse_lazy('authentication:user_list')

    def form_valid(self, form):
        response = super().form_valid(form)

        # تسجيل النشاط
        UserActivity.objects.create(
            user=self.request.user,
            action='create',
            description=f'إنشاء مستخدم جديد: {self.object.username}',
            ip_address=self.request.META.get('REMOTE_ADDR')
        )

        messages.success(self.request, f'تم إنشاء المستخدم {self.object.username} بنجاح')
        return response


class UserUpdateView(AdminRequiredMixin, UpdateView):
    """تحديث بيانات المستخدم"""
    model = User
    form_class = UserUpdateForm
    template_name = 'authentication/user_form.html'
    success_url = reverse_lazy('authentication:user_list')

    def form_valid(self, form):
        response = super().form_valid(form)

        # تسجيل النشاط
        UserActivity.objects.create(
            user=self.request.user,
            action='update',
            description=f'تحديث بيانات المستخدم: {self.object.username}',
            ip_address=self.request.META.get('REMOTE_ADDR')
        )

        messages.success(self.request, f'تم تحديث بيانات المستخدم {self.object.username} بنجاح')
        return response


class UserDeleteView(AdminRequiredMixin, DeleteView):
    """حذف مستخدم"""
    model = User
    template_name = 'authentication/user_confirm_delete.html'
    success_url = reverse_lazy('authentication:user_list')

    def delete(self, request, *args, **kwargs):
        user = self.get_object()

        # تسجيل النشاط
        UserActivity.objects.create(
            user=request.user,
            action='delete',
            description=f'حذف المستخدم: {user.username}',
            ip_address=request.META.get('REMOTE_ADDR')
        )

        messages.success(request, f'تم حذف المستخدم {user.username} بنجاح')
        return super().delete(request, *args, **kwargs)


class UserActivityListView(AdminRequiredMixin, ListView):
    """قائمة نشاطات المستخدمين"""
    model = UserActivity
    template_name = 'authentication/activity_list.html'
    context_object_name = 'activities'
    paginate_by = 50

    def get_queryset(self):
        return UserActivity.objects.select_related('user').order_by('-timestamp')
