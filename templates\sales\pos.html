{% extends 'base.html' %}
{% load humanize %}

{% block title %}نقطة البيع - مكتبة سنتر آدم{% endblock %}

{% block extra_css %}
<style>
    .pos-container {
        height: calc(100vh - 200px);
    }
    
    .product-grid {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .product-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }
    
    .product-card:hover {
        border-color: #667eea;
        transform: translateY(-2px);
    }
    
    .cart-items {
        max-height: 300px;
        overflow-y: auto;
    }
    
    .total-display {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
    }
    
    .barcode-input {
        font-size: 1.2rem;
        padding: 15px;
    }
    
    .quantity-input {
        width: 80px;
    }
    
    .remove-item {
        color: #dc3545;
        cursor: pointer;
    }
    
    .remove-item:hover {
        color: #c82333;
    }
    
    .payment-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h2">
        <i class="fas fa-cash-register me-2"></i>نقطة البيع
    </h1>
    <div>
        <button class="btn btn-outline-secondary me-2" onclick="clearCart()">
            <i class="fas fa-trash me-1"></i>مسح الكل
        </button>
        <a href="{% url 'sales:sale_list' %}" class="btn btn-outline-primary">
            <i class="fas fa-list me-1"></i>المبيعات
        </a>
    </div>
</div>

<div class="row pos-container">
    <!-- قسم المنتجات والبحث -->
    <div class="col-lg-8">
        <!-- شريط البحث والباركود -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label class="form-label">البحث عن منتج</label>
                        <input type="text" id="productSearch" class="form-control" 
                               placeholder="اسم المنتج أو الكود...">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">مسح الباركود</label>
                        <input type="text" id="barcodeInput" class="form-control barcode-input" 
                               placeholder="امسح الباركود هنا..." dir="ltr">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- شبكة المنتجات -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-boxes me-2"></i>المنتجات المتاحة
                </h5>
            </div>
            <div class="card-body">
                <div class="row product-grid" id="productGrid">
                    {% for product in products %}
                    <div class="col-md-4 col-lg-3 mb-3">
                        <div class="card product-card h-100" onclick="addToCart({{ product.id }}, '{{ product.name }}', {{ product.selling_price }}, {{ product.quantity }})">
                            <div class="card-body text-center">
                                {% if product.image %}
                                    <img src="{{ product.image.url }}" class="img-fluid mb-2" style="max-height: 60px;">
                                {% else %}
                                    <i class="fas fa-box fa-2x text-muted mb-2"></i>
                                {% endif %}
                                <h6 class="card-title">{{ product.name }}</h6>
                                <p class="card-text">
                                    <strong>{{ product.selling_price|floatformat:2 }} ج.م</strong><br>
                                    <small class="text-muted">متوفر: {{ product.quantity }}</small>
                                </p>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="col-12 text-center py-4">
                        <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد منتجات متاحة</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- قسم السلة والدفع -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>سلة المشتريات
                </h5>
            </div>
            <div class="card-body">
                <!-- عناصر السلة -->
                <div class="cart-items mb-3" id="cartItems">
                    <div class="text-center py-4" id="emptyCart">
                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                        <p class="text-muted">السلة فارغة</p>
                    </div>
                </div>
                
                <!-- معلومات العميل -->
                <div class="mb-3">
                    <label class="form-label">العميل</label>
                    <select class="form-select" id="customerSelect">
                        <option value="">عميل نقدي</option>
                        {% for customer in customers %}
                        <option value="{{ customer.id }}">{{ customer.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- المجاميع -->
                <div class="payment-section">
                    <div class="row mb-2">
                        <div class="col-6">المجموع الفرعي:</div>
                        <div class="col-6 text-end" id="subtotal">0.00 ج.م</div>
                    </div>
                    
                    <div class="row mb-2">
                        <div class="col-6">
                            <input type="number" class="form-control form-control-sm" 
                                   id="discountPercent" placeholder="خصم %" 
                                   min="0" max="100" step="0.01" onchange="calculateTotal()">
                        </div>
                        <div class="col-6 text-end" id="discountAmount">0.00 ج.م</div>
                    </div>
                    
                    <div class="row mb-2">
                        <div class="col-6">
                            <input type="number" class="form-control form-control-sm" 
                                   id="taxPercent" placeholder="ضريبة %" 
                                   min="0" max="100" step="0.01" onchange="calculateTotal()">
                        </div>
                        <div class="col-6 text-end" id="taxAmount">0.00 ج.م</div>
                    </div>
                    
                    <hr>
                    
                    <div class="row mb-3">
                        <div class="col-6"><strong>الإجمالي:</strong></div>
                        <div class="col-6 text-end total-display" id="total">0.00 ج.م</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">المبلغ المدفوع</label>
                        <input type="number" class="form-control" id="paidAmount" 
                               step="0.01" min="0" onchange="calculateChange()">
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-6">الباقي:</div>
                        <div class="col-6 text-end" id="change">0.00 ج.م</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">طريقة الدفع</label>
                        <select class="form-select" id="paymentMethod">
                            <option value="cash">نقدي</option>
                            <option value="credit">آجل</option>
                            <option value="card">بطاقة</option>
                            <option value="mixed">مختلط</option>
                        </select>
                    </div>
                    
                    <button class="btn btn-primary w-100 btn-lg" onclick="completeSale()" id="completeBtn" disabled>
                        <i class="fas fa-check me-2"></i>إتمام البيع
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
let cart = [];
let subtotal = 0;
let discountAmount = 0;
let taxAmount = 0;
let total = 0;

// إضافة منتج للسلة
function addToCart(productId, productName, price, availableQty) {
    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        if (existingItem.quantity < availableQty) {
            existingItem.quantity++;
            existingItem.total = existingItem.quantity * existingItem.price;
        } else {
            alert('الكمية المطلوبة غير متوفرة');
            return;
        }
    } else {
        cart.push({
            id: productId,
            name: productName,
            price: price,
            quantity: 1,
            total: price,
            availableQty: availableQty
        });
    }
    
    updateCartDisplay();
    calculateTotal();
}

// تحديث عرض السلة
function updateCartDisplay() {
    const cartItems = document.getElementById('cartItems');
    const emptyCart = document.getElementById('emptyCart');
    
    if (cart.length === 0) {
        cartItems.innerHTML = '<div class="text-center py-4" id="emptyCart"><i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i><p class="text-muted">السلة فارغة</p></div>';
        document.getElementById('completeBtn').disabled = true;
        return;
    }
    
    let html = '';
    cart.forEach((item, index) => {
        html += `
            <div class="d-flex justify-content-between align-items-center mb-2 p-2 border rounded">
                <div class="flex-grow-1">
                    <h6 class="mb-1">${item.name}</h6>
                    <small class="text-muted">${item.price.toFixed(2)} ج.م × ${item.quantity}</small>
                </div>
                <div class="d-flex align-items-center">
                    <input type="number" class="form-control quantity-input me-2" 
                           value="${item.quantity}" min="1" max="${item.availableQty}"
                           onchange="updateQuantity(${index}, this.value)">
                    <strong class="me-2">${item.total.toFixed(2)} ج.م</strong>
                    <i class="fas fa-times remove-item" onclick="removeFromCart(${index})"></i>
                </div>
            </div>
        `;
    });
    
    cartItems.innerHTML = html;
    document.getElementById('completeBtn').disabled = false;
}

// تحديث الكمية
function updateQuantity(index, newQuantity) {
    const item = cart[index];
    const qty = parseInt(newQuantity);
    
    if (qty > 0 && qty <= item.availableQty) {
        item.quantity = qty;
        item.total = item.quantity * item.price;
        updateCartDisplay();
        calculateTotal();
    } else {
        alert('الكمية غير صحيحة');
        updateCartDisplay();
    }
}

// إزالة من السلة
function removeFromCart(index) {
    cart.splice(index, 1);
    updateCartDisplay();
    calculateTotal();
}

// مسح السلة
function clearCart() {
    cart = [];
    updateCartDisplay();
    calculateTotal();
}

// حساب المجاميع
function calculateTotal() {
    subtotal = cart.reduce((sum, item) => sum + item.total, 0);
    
    const discountPercent = parseFloat(document.getElementById('discountPercent').value) || 0;
    const taxPercent = parseFloat(document.getElementById('taxPercent').value) || 0;
    
    discountAmount = (subtotal * discountPercent) / 100;
    const taxableAmount = subtotal - discountAmount;
    taxAmount = (taxableAmount * taxPercent) / 100;
    total = taxableAmount + taxAmount;
    
    document.getElementById('subtotal').textContent = subtotal.toFixed(2) + ' ج.م';
    document.getElementById('discountAmount').textContent = discountAmount.toFixed(2) + ' ج.م';
    document.getElementById('taxAmount').textContent = taxAmount.toFixed(2) + ' ج.م';
    document.getElementById('total').textContent = total.toFixed(2) + ' ج.م';
    
    calculateChange();
}

// حساب الباقي
function calculateChange() {
    const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
    const change = Math.max(0, paidAmount - total);
    document.getElementById('change').textContent = change.toFixed(2) + ' ج.م';
}

// إتمام البيع
function completeSale() {
    if (cart.length === 0) {
        alert('السلة فارغة');
        return;
    }
    
    const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
    const paymentMethod = document.getElementById('paymentMethod').value;
    
    if (paymentMethod === 'cash' && paidAmount < total) {
        alert('المبلغ المدفوع أقل من الإجمالي');
        return;
    }
    
    // إرسال البيانات للخادم
    const saleData = {
        customer: document.getElementById('customerSelect').value || null,
        payment_method: paymentMethod,
        discount_percentage: parseFloat(document.getElementById('discountPercent').value) || 0,
        tax_percentage: parseFloat(document.getElementById('taxPercent').value) || 0,
        paid_amount: paidAmount,
        items: cart,
        subtotal: subtotal,
        discount_amount: discountAmount,
        tax_amount: taxAmount,
        total_amount: total
    };
    
    // هنا يمكن إرسال البيانات عبر AJAX
    console.log('Sale Data:', saleData);
    alert('تم إتمام البيع بنجاح!');
    
    // مسح السلة
    clearCart();
    document.getElementById('paidAmount').value = '';
    document.getElementById('discountPercent').value = '';
    document.getElementById('taxPercent').value = '';
    document.getElementById('customerSelect').value = '';
}

// البحث عن المنتجات
document.getElementById('productSearch').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    // هنا يمكن إضافة منطق البحث عبر AJAX
});

// مسح الباركود
document.getElementById('barcodeInput').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        const barcode = this.value;
        // هنا يمكن البحث عن المنتج بالباركود عبر AJAX
        this.value = '';
    }
});

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    calculateTotal();
});
</script>
{% endblock %}
